<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Gemini AI Configuration
 * 
 * This configuration class manages all Gemini AI related settings
 * for the DERS system, providing centralized configuration management.
 */
class Gemini extends BaseConfig
{
    /**
     * Gemini API Key
     * 
     * @var string
     */
    public string $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';

    /**
     * Gemini API Base URL
     * 
     * @var string
     */
    public string $baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

    /**
     * Default Gemini Model
     * 
     * @var string
     */
    public string $model = 'gemini-2.0-flash';

    /**
     * API Request Timeout (in seconds)
     * 
     * @var int
     */
    public int $timeout = 240; // 4 minutes for file processing

    /**
     * Standard API Request Timeout (in seconds)
     * For text generation and analysis
     * 
     * @var int
     */
    public int $standardTimeout = 60;

    /**
     * Maximum file size for inline processing (in bytes)
     * Files larger than this will use the File API
     * 
     * @var int
     */
    public int $maxInlineFileSize = 20971520; // 20MB

    /**
     * Maximum file size allowed for processing (in bytes)
     * 
     * @var int
     */
    public int $maxFileSize = 26214400; // 25MB

    /**
     * Default Generation Configuration for Text Extraction
     * 
     * @var array
     */
    public array $textExtractionConfig = [
        'temperature' => 0.1,
        'topK' => 32,
        'topP' => 0.95,
        'maxOutputTokens' => 8192,
        'responseMimeType' => 'text/plain'
    ];

    /**
     * Default Generation Configuration for Profile Generation
     * 
     * @var array
     */
    public array $profileGenerationConfig = [
        'temperature' => 0.7,
        'topK' => 40,
        'topP' => 0.8,
        'maxOutputTokens' => 4096,
        'responseMimeType' => 'text/plain'
    ];

    /**
     * Default Generation Configuration for Applicant Analysis
     * 
     * @var array
     */
    public array $analysisConfig = [
        'temperature' => 0.4,
        'topK' => 40,
        'topP' => 0.8,
        'maxOutputTokens' => 4096,
        'responseMimeType' => 'text/plain'
    ];

    /**
     * Safety Settings for Content Filtering
     * 
     * @var array
     */
    public array $safetySettings = [
        [
            'category' => 'HARM_CATEGORY_HARASSMENT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_HATE_SPEECH',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ]
    ];

    /**
     * Supported MIME types for file processing
     * 
     * @var array
     */
    public array $supportedMimeTypes = [
        // PDF files
        'application/pdf',
        
        // Microsoft Office documents
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/msword', // .doc
        'application/vnd.ms-powerpoint', // .ppt
        'application/vnd.ms-excel', // .xls
        
        // Text files
        'text/plain',
        'text/csv',
        'text/html',
        'text/markdown',
        
        // Images
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/tiff',
        
        // Other formats
        'application/rtf'
    ];

    /**
     * File extension to MIME type mapping
     * 
     * @var array
     */
    public array $extensionMimeMap = [
        'pdf' => 'application/pdf',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'doc' => 'application/msword',
        'ppt' => 'application/vnd.ms-powerpoint',
        'xls' => 'application/vnd.ms-excel',
        'txt' => 'text/plain',
        'csv' => 'text/csv',
        'html' => 'text/html',
        'htm' => 'text/html',
        'md' => 'text/markdown',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'bmp' => 'image/bmp',
        'tiff' => 'image/tiff',
        'tif' => 'image/tiff',
        'rtf' => 'application/rtf'
    ];

    /**
     * Enable/disable detailed logging
     * 
     * @var bool
     */
    public bool $enableLogging = true;

    /**
     * Enable/disable debug mode for development
     * 
     * @var bool
     */
    public bool $debugMode = false;

    /**
     * Get the full API URL for content generation
     * 
     * @return string
     */
    public function getGenerateContentUrl(): string
    {
        return $this->baseUrl . "/models/{$this->model}:generateContent?key=" . $this->apiKey;
    }

    /**
     * Get the full API URL for file upload
     * 
     * @return string
     */
    public function getFileUploadUrl(): string
    {
        return $this->baseUrl . "/files?key=" . $this->apiKey;
    }

    /**
     * Check if a MIME type is supported
     * 
     * @param string $mimeType
     * @return bool
     */
    public function isSupportedMimeType(string $mimeType): bool
    {
        return in_array($mimeType, $this->supportedMimeTypes);
    }

    /**
     * Get MIME type from file extension
     * 
     * @param string $extension
     * @return string|null
     */
    public function getMimeTypeFromExtension(string $extension): ?string
    {
        $extension = strtolower($extension);
        return $this->extensionMimeMap[$extension] ?? null;
    }

    /**
     * Get configuration for specific operation type
     * 
     * @param string $type ('extraction', 'profile', 'analysis')
     * @return array
     */
    public function getGenerationConfig(string $type = 'extraction'): array
    {
        switch ($type) {
            case 'profile':
                return $this->profileGenerationConfig;
            case 'analysis':
                return $this->analysisConfig;
            case 'extraction':
            default:
                return $this->textExtractionConfig;
        }
    }
}
