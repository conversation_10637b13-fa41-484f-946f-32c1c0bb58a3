<?php

// Remove the GuzzleHttp dependency
// use GuzzleHttp\Client;

/**
 * Helper file for Gemini API integration
 * This file provides functions to interact with Google's Gemini AI API
 */

if (!function_exists('gemini_generate_profile')) {
    /**
     * Generate a comprehensive profile using Google's Gemini AI API
     * 
     * @param array $data The data to send to Gemini AI
     * @param string $apiKey The Gemini API key
     * @return array Response with success status and generated content or error message
     */
    function gemini_generate_profile(array $data, string $apiKey = null)
    {
        // Use the default API key if not provided
        if (empty($apiKey)) {
            // Get API key from configuration
            $config = new \Config\Gemini();
            $apiKey = $config->apiKey;
        }

        // Initialize cURL
        $curl = curl_init();

        // Prepare the request body
        $requestBody = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => "Generate a comprehensive professional profile for this applicant based on the information provided. The profile should follow this exact structure:

I. Core Identifiers & Contact Information:
- Name: Full name of the applicant.
- Date of Birth and Age: Applicant's age.
- Sex: Male/Female.
- Address/Location: Current residential or work location.
- Contact Details: Phone number(s), Email address(es).
- Place of Origin: Village, District, Province.
- NID Number: National Identification number.

II. Employment Information:
- Current Position: Job title of the applicant's current role.
- Current Employer: Name of the organization the applicant currently works for.
- Public Service Status: Whether the applicant is currently a public servant or not.

III. Education & Training:
- Qualifications: Formal educational achievements (e.g., Degrees, Diplomas, Certificates - compared against Job Description).
- Other Training/Courses Attended: Additional relevant professional development, workshops, certifications etc.

IV. Knowledge, Skills, and Competencies:
- Knowledge: Specific areas of expertise, technical understanding, theoretical knowledge relevant to the position.
- Skills/Competencies: Practical abilities, technical skills (e.g., computer literacy), soft skills (e.g., leadership, communication, planning).

V. Experience:
- Related Job Experience: Detailed history of previous roles, responsibilities, organizations, and durations, focusing on relevance to the target position.

VI. Performance & Achievements:
- Performance Level: Assessment of past job performance or potential based on experience.
- Publications: Any relevant published work.
- Awards: Any relevant awards or recognitions received.

VII. Verification (Supporting Information):
- Referees: Contact details for professional references.

For each section, use ONLY the data provided. DO NOT invent or assume additional information. If data for a section is missing, simply state 'Information not available' for that section.

The profile should be professionally written, concise, and highlight the most important aspects of the candidate's background and potential fit for the position. Use a formal tone throughout.

Focus especially on comparing the applicant's qualifications against the job requirements and highlighting relevant experience and skills for the position they've applied for.

Here is the applicant data in a structured format:\n\n" . json_encode($data, JSON_PRETTY_PRINT)
                        ]
                    ]
                ]
            ],
            'generationConfig' => (new \Config\Gemini())->getGenerationConfig('profile')
        ];

        // Set cURL options with improved configuration
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" . $apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => (new \Config\Gemini())->standardTimeout, // Configurable timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($requestBody),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ],
        ]);

        // Execute the request
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        // Handle the response
        if ($err) {
            log_message('error', 'Gemini API Error: ' . $err);
            return [
                'success' => false,
                'message' => 'Error connecting to Gemini API: ' . $err
            ];
        }

        // Parse the response
        $responseData = json_decode($response, true);

        if ($statusCode !== 200 || empty($responseData) || isset($responseData['error'])) {
            $errorMessage = isset($responseData['error']['message']) 
                ? $responseData['error']['message'] 
                : 'Unknown error with Gemini API';
            
            log_message('error', 'Gemini API Response Error: ' . $errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error from Gemini API: ' . $errorMessage
            ];
        }

        // Check if we have text in the response
        if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'profile' => $responseData['candidates'][0]['content']['parts'][0]['text']
            ];
        }

        // Fallback error if response structure isn't as expected
        return [
            'success' => false,
            'message' => 'Unexpected response structure from Gemini API'
        ];
    }
}

function generate_profile($data) {
    if (empty($data)) {
        return "No data provided for profile generation.";
    }

    // Settings for Gemini API
    $apiKey = getenv('GEMINI_API_KEY');
    if (empty($apiKey)) {
        return "API key is not configured. Please set the GEMINI_API_KEY environment variable.";
    }

    // Use cURL instead of Guzzle client
    $curl = curl_init();
    $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" . $apiKey;

    // Build a prompt for generating a professional profile
    $prompt = "You are a professional HR assistant. Generate a detailed employee profile report based on the following structured information. Use only the data provided:

**STRUCTURED EMPLOYEE PROFILE REPORT**

This report should follow this structure with 7 main sections. For each section, use only the data provided. If information is not available for any specific section, state 'Information not available' rather than making assumptions:

I. Core Identifiers & Contact Information
- Full name 
- Date of birth and age
- Sex
- Address/location
- Contact details
- Place of origin
- National ID number
- Citizenship
- Include spouse information, children, or offense conviction information if provided

II. Employment Information
- Current position
- Current employer
- Current salary
- Public service status

III. Education & Training
- Formal education qualifications (list all provided)
- Position applied for (title and requirements)
- Training courses attended (list all with dates)

IV. Knowledge, Skills, and Competencies
- Knowledge areas (derived from education and experience)
- Skills and competencies (derived from work descriptions and achievements)
- Analyze how these match the requirements of the position applied for

V. Experience
- Related job experience (chronologically ordered)
- Experience summary (total years, positions, earliest experience)
- Highlight public service experience specifically

VI. Performance & Achievements
- Performance level (if available)
- Publications (if available)
- Awards and recognitions (if available)

VII. Verification
- References provided

Write professionally and formally. Be concise but comprehensive. Format the profile in a clean, section-by-section structure with proper headings and bullet points where appropriate. 

The profile should be suitable for HR review in a government recruitment context. Focus on presenting factual information without subjective judgment.";

    // Prepare the request payload
    $requestBody = [
        'contents' => [
            [
                'role' => 'user',
                'parts' => [
                    [
                        'text' => $prompt . "\n\nData: " . json_encode($data, JSON_PRETTY_PRINT)
                    ]
                ]
            ]
        ],
        'generationConfig' => (new \Config\Gemini())->getGenerationConfig('profile')
    ];

    try {
        // Set cURL options
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => (new \Config\Gemini())->standardTimeout, // Configurable timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($requestBody),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ],
        ]);

        // Execute the request
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        // Handle any cURL errors
        if ($err) {
            log_message('error', 'Gemini API Error: ' . $err);
            return "Error connecting to Gemini API: " . $err;
        }

        // Parse the response
        $result = json_decode($response, true);
        
        if ($statusCode !== 200 || empty($result) || isset($result['error'])) {
            $errorMessage = isset($result['error']['message']) 
                ? $result['error']['message'] 
                : 'Unknown error with Gemini API';
            
            log_message('error', 'Gemini API Response Error: ' . $errorMessage);
            return "Error from Gemini API: " . $errorMessage;
        }
        
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return $result['candidates'][0]['content']['parts'][0]['text'];
        } else {
            return "Error: Unable to generate profile. Response format not as expected.";
        }
    } catch (\Exception $e) {
        return "Error generating profile: " . $e->getMessage();
    }
}

if (!function_exists('gemini_analyze_applicant')) {
    /**
     * Analyze applicant data using Gemini AI
     * 
     * @param array $data Data containing applicant and position information
     * @param string $prompt Custom prompt for the analysis
     * @param string $apiKey Optional API key
     * @return array Response with success status and analysis content or error message
     */
    function gemini_analyze_applicant(array $data, string $prompt = '', string $apiKey = null)
    {
        // Use the default API key if not provided
        if (empty($apiKey)) {
            // Get API key from configuration
            $config = new \Config\Gemini();
            $apiKey = $config->apiKey;
        }

        // Initialize cURL
        $curl = curl_init();

        // Prepare the request body
        $requestBody = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt . "\n\n" . json_encode($data, JSON_PRETTY_PRINT)
                        ]
                    ]
                ]
            ],
            'generationConfig' => (new \Config\Gemini())->getGenerationConfig('analysis')
        ];

        // Set cURL options with improved configuration
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" . $apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => (new \Config\Gemini())->standardTimeout, // Configurable timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($requestBody),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ],
        ]);

        // Execute the request
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        // Handle the response
        if ($err) {
            log_message('error', 'Gemini API Error: ' . $err);
            return [
                'success' => false,
                'message' => 'Error connecting to Gemini API: ' . $err
            ];
        }

        // Parse the response
        $responseData = json_decode($response, true);

        if ($statusCode !== 200 || empty($responseData) || isset($responseData['error'])) {
            $errorMessage = isset($responseData['error']['message']) 
                ? $responseData['error']['message'] 
                : 'Unknown error with Gemini API';
            
            log_message('error', 'Gemini API Response Error: ' . $errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error from Gemini API: ' . $errorMessage
            ];
        }

        // Check if we have text in the response
        if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'analysis' => $responseData['candidates'][0]['content']['parts'][0]['text']
            ];
        }

        // Fallback error if response structure isn't as expected
        return [
            'success' => false,
            'message' => 'Unexpected response structure from Gemini API'
        ];
    }
} 