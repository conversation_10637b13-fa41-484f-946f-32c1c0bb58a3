<?php

namespace App\Services;

use Exception;
use Config\Gemini as GeminiConfig;

class GeminiService
{
    private $apiKey;
    private $baseUrl;
    private $model;
    private $config;

    public function __construct()
    {
        $this->config = new GeminiConfig();
        $this->apiKey = $this->config->apiKey;
        $this->baseUrl = $this->config->baseUrl;
        $this->model = $this->config->model;
    }

    /**
     * Extract text from a file using Gemini AI
     *
     * @param string $filePath Full path to the file
     * @param string $mimeType MIME type of the file
     * @return string|null Extracted text or null on failure
     */
    public function extractTextFromFile($filePath, $mimeType)
    {
        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                log_message('error', "GeminiService: File not found: {$filePath}");
                echo "ERROR: File not found: {$filePath}\n";
                return null;
            }

            // Validate and normalize MIME type
            $mimeType = $this->validateMimeType($filePath, $mimeType);
            if (!$mimeType) {
                log_message('error', "GeminiService: Could not determine valid MIME type for: {$filePath}");
                echo "ERROR: Could not determine valid MIME type for file\n";
                return null;
            }

            // Get file size and estimate processing complexity
            $fileSize = filesize($filePath);
            $fileSizeMB = round($fileSize / (1024 * 1024), 2);

            log_message('info', "GeminiService: Processing file {$filePath}, size: " . number_format($fileSize / 1024, 2) . " KB, MIME: {$mimeType}");
            echo "GeminiService: Starting extraction for {$fileSizeMB}MB file ({$mimeType})\n";

            // Estimate processing time based on file size
            $estimatedTime = $this->estimateProcessingTime($fileSize, $mimeType);
            echo "Estimated processing time: {$estimatedTime} seconds\n";

            // For files under configured limit, use inline data
            if ($fileSize < $this->config->maxInlineFileSize) {
                log_message('info', "GeminiService: Using inline data method for file extraction");
                echo "Using inline data method (file < " . round($this->config->maxInlineFileSize / (1024 * 1024)) . "MB)\n";
                return $this->extractTextInline($filePath, $mimeType);
            } else {
                // For larger files, use File API
                log_message('info', "GeminiService: Using File API method for large file extraction");
                echo "Using File API method (file >= " . round($this->config->maxInlineFileSize / (1024 * 1024)) . "MB)\n";
                return $this->extractTextViaFileAPI($filePath, $mimeType);
            }
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error extracting text: " . $e->getMessage());
            log_message('error', "GeminiService: Stack trace: " . $e->getTraceAsString());
            echo "ERROR: " . $e->getMessage() . "\n";
            return null;
        }
    }

    /**
     * Estimate processing time based on file size and type
     */
    private function estimateProcessingTime($fileSize, $mimeType)
    {
        $basetime = 10; // Base 10 seconds
        $sizeMultiplier = ($fileSize / (1024 * 1024)) * 2; // 2 seconds per MB

        // Different file types have different complexity
        if (strpos($mimeType, 'pdf') !== false) {
            $typeMultiplier = 1.5; // PDFs are more complex
        } elseif (strpos($mimeType, 'image') !== false) {
            $typeMultiplier = 2.0; // Images require OCR
        } else {
            $typeMultiplier = 1.0; // Documents are simpler
        }

        return round($basetime + ($sizeMultiplier * $typeMultiplier));
    }

    /**
     * Validate and normalize MIME type
     */
    private function validateMimeType($filePath, $providedMimeType)
    {
        // If provided MIME type is valid, use it
        if ($providedMimeType && $this->isSupportedFileType($providedMimeType)) {
            return $providedMimeType;
        }

        // Try to detect MIME type from file extension as fallback
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypeMap = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'webp' => 'image/webp',
            'txt' => 'text/plain'
        ];

        if (isset($mimeTypeMap[$extension])) {
            log_message('info', "GeminiService: Using MIME type from extension: {$extension} -> {$mimeTypeMap[$extension]}");
            return $mimeTypeMap[$extension];
        }

        // Try using PHP's finfo if available and file exists
        if (function_exists('finfo_open') && file_exists($filePath)) {
            try {
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $detectedMimeType = finfo_file($finfo, $filePath);
                finfo_close($finfo);

                if ($detectedMimeType && $this->isSupportedFileType($detectedMimeType)) {
                    log_message('info', "GeminiService: Detected MIME type: {$detectedMimeType}");
                    return $detectedMimeType;
                }
            } catch (Exception $e) {
                log_message('warning', "GeminiService: finfo detection failed: " . $e->getMessage());
            }
        }

        return null;
    }

    /**
     * Extract text using inline data (for files < 20MB)
     */
    private function extractTextInline($filePath, $mimeType)
    {
        try {
            echo "Step 1/4: Reading file data...\n";

            // Read and encode file
            $fileContent = file_get_contents($filePath);
            $fileSize = strlen($fileContent);
            echo "File data read: " . number_format($fileSize) . " bytes\n";

            echo "Step 2/4: Encoding file to base64...\n";
            $base64Content = base64_encode($fileContent);
            echo "Base64 encoding complete: " . number_format(strlen($base64Content)) . " characters\n";

            echo "Step 3/4: Preparing AI request with complete extraction prompt...\n";

            // Prepare the prompt based on file type
            $prompt = $this->getExtractionPrompt($mimeType);

            // Prepare request data with enhanced generation config
            $requestData = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $base64Content
                                ]
                            ],
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => $this->config->getGenerationConfig('extraction')
            ];

            echo "Step 4/4: Sending request to Gemini AI for complete text extraction...\n";
            echo "AI is now processing ALL pages - this may take several minutes for large documents\n";

            // Make API request
            $startTime = microtime(true);
            $response = $this->makeApiRequest($requestData);
            $processingTime = round(microtime(true) - $startTime, 2);

            echo "AI processing completed in {$processingTime} seconds\n";

            if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                $extractedText = $response['candidates'][0]['content']['parts'][0]['text'];

                // Analyze the extracted content
                $textLength = strlen($extractedText);
                $wordCount = str_word_count($extractedText);
                $lineCount = substr_count($extractedText, "\n");
                $pageBreaks = substr_count(strtolower($extractedText), 'page') + substr_count($extractedText, "\f");

                echo "EXTRACTION COMPLETE!\n";
                echo "- Text length: " . number_format($textLength) . " characters\n";
                echo "- Word count: " . number_format($wordCount) . " words\n";
                echo "- Line count: " . number_format($lineCount) . " lines\n";
                echo "- Detected page indicators: {$pageBreaks}\n";
                echo "- Processing rate: " . round($textLength / max($processingTime, 1)) . " chars/second\n";

                // Log the extraction result
                log_message('info', "GeminiService: Extracted text length: " . strlen($extractedText) . " characters in {$processingTime}s");

                // Check if the response seems complete (basic validation)
                if (strlen($extractedText) < 100) {
                    log_message('warning', "GeminiService: Extracted text seems too short, may be incomplete");
                    echo "WARNING: Extracted text seems unusually short (< 100 characters)\n";
                } else {
                    echo "Text extraction appears successful - good content length detected\n";
                }

                return $extractedText;
            } else {
                echo "ERROR: No text content returned from AI service\n";
                log_message('error', "GeminiService: No text content in API response");
            }

            return null;
        } catch (Exception $e) {
            echo "ERROR in text extraction: " . $e->getMessage() . "\n";
            log_message('error', "GeminiService: Error in inline extraction: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract text using File API (for files >= 20MB)
     */
    private function extractTextViaFileAPI($filePath, $mimeType)
    {
        try {
            echo "Large file detected - using File API method\n";

            // Upload file first
            echo "Step 1/3: Uploading file to Gemini File API...\n";
            $fileUri = $this->uploadFileToGemini($filePath, $mimeType);

            if (!$fileUri) {
                echo "ERROR: Failed to upload file to Gemini File API\n";
                return null;
            }

            echo "File uploaded successfully: {$fileUri}\n";
            echo "Step 2/3: Preparing extraction request...\n";

            // Prepare the prompt
            $prompt = $this->getExtractionPrompt($mimeType);

            // Prepare request data with enhanced generation config
            $requestData = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'file_data' => [
                                    'mime_type' => $mimeType,
                                    'file_uri' => $fileUri
                                ]
                            ],
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => $this->config->getGenerationConfig('extraction')
            ];

            echo "Step 3/3: Processing with Gemini AI (large file processing)...\n";
            echo "AI is extracting complete text from ALL pages - this may take several minutes\n";

            // Make API request
            $startTime = microtime(true);
            $response = $this->makeApiRequest($requestData);
            $processingTime = round(microtime(true) - $startTime, 2);

            echo "Large file processing completed in {$processingTime} seconds\n";

            if ($response && isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                $extractedText = $response['candidates'][0]['content']['parts'][0]['text'];

                // Analyze the extracted content
                $textLength = strlen($extractedText);
                $wordCount = str_word_count($extractedText);
                $lineCount = substr_count($extractedText, "\n");

                echo "LARGE FILE EXTRACTION COMPLETE!\n";
                echo "- Text length: " . number_format($textLength) . " characters\n";
                echo "- Word count: " . number_format($wordCount) . " words\n";
                echo "- Line count: " . number_format($lineCount) . " lines\n";
                echo "- Processing rate: " . round($textLength / max($processingTime, 1)) . " chars/second\n";

                return $extractedText;
            } else {
                echo "ERROR: No text content returned from File API\n";
            }

            return null;
        } catch (Exception $e) {
            echo "ERROR in File API extraction: " . $e->getMessage() . "\n";
            log_message('error', "GeminiService: Error in File API extraction: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Upload file to Gemini File API
     */
    private function uploadFileToGemini($filePath, $mimeType)
    {
        try {
            $fileSize = filesize($filePath);
            $displayName = basename($filePath);

            echo "Uploading {$displayName} (" . number_format($fileSize / (1024*1024), 2) . " MB) to Gemini...\n";

            // Step 1: Initialize resumable upload
            echo "Initializing resumable upload...\n";
            $initUrl = $this->baseUrl . "/upload/v1beta/files?key=" . $this->apiKey;
            
            $initHeaders = [
                'X-Goog-Upload-Protocol: resumable',
                'X-Goog-Upload-Command: start',
                'X-Goog-Upload-Header-Content-Length: ' . $fileSize,
                'X-Goog-Upload-Header-Content-Type: ' . $mimeType,
                'Content-Type: application/json'
            ];

            $initData = json_encode([
                'file' => [
                    'display_name' => $displayName
                ]
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $initUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $initData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $initHeaders);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                echo "ERROR: Failed to initialize upload. HTTP Code: {$httpCode}\n";
                log_message('error', "GeminiService: Failed to initialize upload. HTTP Code: {$httpCode}");
                return null;
            }

            echo "Upload initialized successfully\n";

            // Extract upload URL from headers
            preg_match('/x-goog-upload-url:\s*(.+)/i', $response, $matches);
            if (!isset($matches[1])) {
                echo "ERROR: Could not extract upload URL from response\n";
                log_message('error', "GeminiService: Could not extract upload URL from response");
                return null;
            }

            $uploadUrl = trim($matches[1]);

            // Step 2: Upload file content
            echo "Uploading file content (" . number_format($fileSize / 1024, 2) . " KB)...\n";
            $uploadHeaders = [
                'Content-Length: ' . $fileSize,
                'X-Goog-Upload-Offset: 0',
                'X-Goog-Upload-Command: upload, finalize'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $uploadUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, file_get_contents($filePath));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $uploadHeaders);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $uploadResponse = curl_exec($ch);
            $uploadHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($uploadHttpCode !== 200) {
                echo "ERROR: Failed to upload file. HTTP Code: {$uploadHttpCode}\n";
                log_message('error', "GeminiService: Failed to upload file. HTTP Code: {$uploadHttpCode}");
                return null;
            }

            echo "File upload completed successfully\n";

            $uploadData = json_decode($uploadResponse, true);

            if (isset($uploadData['file']['uri'])) {
                echo "File URI obtained: " . $uploadData['file']['uri'] . "\n";
                return $uploadData['file']['uri'];
            }

            echo "ERROR: No file URI in upload response\n";
            return null;
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error uploading file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Make API request to Gemini
     */
    private function makeApiRequest($requestData)
    {
        try {
            $url = $this->baseUrl . "/models/{$this->model}:generateContent?key=" . $this->apiKey;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $this->config->timeout); // Configurable timeout for file processing

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                $errorMessage = "GeminiService: API request failed. HTTP Code: {$httpCode}";

                // Parse error response for better logging
                $errorData = json_decode($response, true);
                if (isset($errorData['error']['message'])) {
                    $errorMessage .= ", Error: " . $errorData['error']['message'];

                    // Check for quota/billing issues
                    if (isset($errorData['error']['code']) && $errorData['error']['code'] == 429) {
                        $errorMessage .= " (Quota exceeded - consider switching to gemini-1.5-flash for free tier)";
                    }
                }

                log_message('error', $errorMessage);
                return null;
            }

            return json_decode($response, true);
        } catch (Exception $e) {
            log_message('error', "GeminiService: Error making API request: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get appropriate extraction prompt based on file type
     */
    private function getExtractionPrompt($mimeType)
    {
        switch ($mimeType) {
            case 'application/pdf':
                return 'CRITICAL INSTRUCTIONS: You MUST extract ALL text content from EVERY SINGLE PAGE of this PDF document. Do NOT summarize, skip, or omit any text. Extract the COMPLETE FULL TEXT from page 1 to the last page.

REQUIREMENTS:
1. Extract ALL text from ALL pages - do not stop until you reach the last page
2. Include EVERY word, sentence, paragraph, header, footer, and caption
3. Do NOT summarize or condense any content
4. Do NOT skip any pages or sections
5. Include page numbers or page breaks where visible
6. Extract text from tables, charts, diagrams, and any visual elements
7. Preserve the complete document structure and hierarchy
8. Format the extracted text in markdown with:
   - # for main headings, ## for subheadings, ### for smaller headings
   - **bold** for important text or titles
   - *italic* for emphasis
   - > for quotes or highlighted sections
   - - or * for bullet points
   - numbered lists (1., 2., 3.) where appropriate
   - tables (| column | column |) for tabular data
   - `code` for any technical terms or codes

IMPORTANT: This is a complete text extraction task. Extract EVERYTHING from EVERY page. The output should contain the full, complete text of the entire document.';

            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'CRITICAL INSTRUCTIONS: You MUST extract ALL text content from EVERY SINGLE PAGE of this document. Do NOT summarize, skip, or omit any text. Extract the COMPLETE FULL TEXT from beginning to end.

REQUIREMENTS:
1. Extract ALL text from ALL pages - do not stop until you reach the end
2. Include EVERY word, sentence, paragraph, header, footer, and caption
3. Do NOT summarize or condense any content
4. Do NOT skip any pages or sections
5. Include page numbers or page breaks where visible
6. Extract text from tables, charts, diagrams, and any embedded content
7. Preserve the complete document structure, headings, and formatting
8. Format the extracted text in markdown with:
   - # for main headings, ## for subheadings, ### for smaller headings
   - **bold** for important text or titles
   - *italic* for emphasis
   - > for quotes or highlighted sections
   - - or * for bullet points
   - numbered lists (1., 2., 3.) where appropriate
   - tables (| column | column |) for tabular data
   - `code` for any technical terms or codes

IMPORTANT: This is a complete text extraction task. Extract EVERYTHING from the entire document. The output should contain the full, complete text of the entire document.';

            case 'image/jpeg':
            case 'image/jpg':
            case 'image/png':
            case 'image/webp':
                return 'CRITICAL INSTRUCTIONS: You MUST extract ALL visible text from this image. Do NOT summarize, skip, or omit any text. Extract EVERY SINGLE word and character that is visible.

REQUIREMENTS:
1. Extract ALL visible text - do not miss any words or characters
2. Include EVERY word, number, symbol, and text element visible in the image
3. Do NOT summarize or condense any content
4. Include text from documents, signs, labels, forms, or any other readable content
5. Preserve the text structure and formatting as much as possible
6. If the image contains multiple sections or pages, extract from all of them
7. Format the extracted text in markdown with:
   - # for main headings, ## for subheadings, ### for smaller headings
   - **bold** for important text or titles
   - *italic* for emphasis
   - > for quotes or highlighted sections
   - - or * for bullet points
   - numbered lists (1., 2., 3.) where appropriate
   - tables (| column | column |) for tabular data
   - `code` for any technical terms, reference numbers, or codes

IMPORTANT: This is a complete text extraction task. Extract EVERYTHING that is readable in the image.';

            default:
                return 'CRITICAL INSTRUCTIONS: You MUST extract ALL text content from this file. Do NOT summarize, skip, or omit any text. Extract the COMPLETE FULL TEXT.

REQUIREMENTS:
1. Extract ALL text from the entire file - do not stop until you reach the end
2. Include EVERY word, sentence, paragraph, and text element
3. Do NOT summarize or condense any content
4. Do NOT skip any sections
5. Include all readable text and describe any visual elements that contain information
6. Format the extracted text in markdown with:
   - # for main headings, ## for subheadings, ### for smaller headings
   - **bold** for important text or titles
   - *italic* for emphasis
   - > for quotes or highlighted sections
   - - or * for bullet points
   - numbered lists (1., 2., 3.) where appropriate
   - tables (| column | column |) for tabular data
   - `code` for any technical terms or codes

IMPORTANT: This is a complete text extraction task. Extract EVERYTHING from the entire file.';
        }
    }

    /**
     * Check if file type is supported for text extraction
     */
    public function isSupportedFileType($mimeType)
    {
        $supportedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/webp',
            'text/plain'
        ];

        return in_array($mimeType, $supportedTypes);
    }
}
