<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">Job Openings</h1>
            <p class="text-muted">Browse through available job exercises and click to view positions.</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?= base_url('applicant/jobs') ?>" class="row g-3">
                <div class="col-md-4">
                    <label for="org_id" class="form-label">Filter by Organization</label>
                    <select name="org_id" id="org_id" class="form-select">
                        <option value="">All Organizations</option>
                        <?php foreach ($organizations as $org): ?>
                            <option value="<?= $org['id'] ?>" <?= $selectedOrgId == $org['id'] ? 'selected' : '' ?>>
                                <?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="Search by organization, exercise name, or description..."
                           value="<?= esc($searchTerm ?? '') ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>

            <!-- Filter Results Summary -->
            <?php if ($selectedOrgId || $searchTerm): ?>
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">
                                Showing <?= $filteredJobs ?> of <?= $totalJobs ?> job exercises
                                <?php if ($selectedOrgId): ?>
                                    <?php
                                    $selectedOrgName = '';
                                    foreach ($organizations as $org) {
                                        if ($org['id'] == $selectedOrgId) {
                                            $selectedOrgName = $org['org_name'];
                                            break;
                                        }
                                    }
                                    ?>
                                    • Filtered by: <strong><?= esc($selectedOrgName) ?></strong>
                                <?php endif; ?>
                                <?php if ($searchTerm): ?>
                                    • Search: <strong>"<?= esc($searchTerm) ?>"</strong>
                                <?php endif; ?>
                            </span>
                        </div>
                        <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="mt-3 pt-3 border-top">
                    <span class="text-muted">Showing all <?= $totalJobs ?> job exercises</span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- View Toggle Buttons -->
    <?php if (!empty($jobData)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">Available Job Exercises</h5>
            <div class="btn-group" role="group" aria-label="View toggle">
                <button type="button" class="btn btn-outline-secondary active" id="gridViewBtn" onclick="toggleView('grid')">
                    <i class="fas fa-th me-2"></i>Grid
                </button>
                <button type="button" class="btn btn-outline-secondary" id="tableViewBtn" onclick="toggleView('table')">
                    <i class="fas fa-list me-2"></i>Table
                </button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (empty($jobData)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h5>No Job Openings Available</h5>
                <p class="text-muted">There are currently no published job exercises.</p>
                <?php if (isset($error)): ?>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i><?= esc($error) ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- Grid View -->
        <div class="grid-view active" id="gridView">
            <div class="row">
                <?php foreach ($jobData as $data): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <?= esc($data['organization']['org_name']) ?>
                                </h5>
                                <?php if (!empty($data['organization']['orglogo'])): ?>
                                    <div class="text-center mb-4">
                                        <img src="<?= base_url(str_replace('public/', '', $data['organization']['orglogo'])) ?>"
                                             alt="Organization Logo"
                                             class="img-fluid"
                                             style="max-height: 100px;">
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-subtitle text-muted">
                                        Exercise #<?= esc($data['exercise']['gazzetted_no'] ?? 'N/A') ?>
                                    </h6>
                                    <span class="badge bg-success">
                                        <?= $data['position_count'] ?> Position<?= $data['position_count'] > 1 ? 's' : '' ?>
                                    </span>
                                </div>
                                <p class="card-text">
                                    <strong>Exercise:</strong> <?= esc($data['exercise']['exercise_name']) ?><br>
                                    <strong>Advertisement No:</strong> <?= esc($data['exercise']['advertisement_no'] ?? 'N/A') ?><br>
                                    <strong>Published:</strong> <?= date('M d, Y', strtotime($data['exercise']['publish_date_from'])) ?><br>
                                    <strong>Closes:</strong> <?= date('M d, Y', strtotime($data['exercise']['publish_date_to'])) ?>
                                </p>
                                <div class="text-end">
                                    <a href="<?= base_url('applicant/jobs/' . $data['exercise']['id']) ?>"
                                       class="btn btn-primary">
                                        <i class="fas fa-eye me-2"></i>View Positions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Table View -->
        <div class="table-view" id="tableView" style="display: none;">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Organization</th>
                                    <th>Exercise Name</th>
                                    <th>Advertisement No</th>
                                    <th>Positions</th>
                                    <th>Published</th>
                                    <th>Closes</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($jobData as $data): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($data['organization']['orglogo'])): ?>
                                                    <img src="<?= base_url(str_replace('public/', '', $data['organization']['orglogo'])) ?>"
                                                         alt="Logo" class="me-2" style="width: 30px; height: 30px; object-fit: contain;">
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?= esc($data['organization']['org_name']) ?></strong><br>
                                                    <small class="text-muted"><?= esc($data['organization']['org_code']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?= esc($data['exercise']['exercise_name']) ?></strong><br>
                                            <small class="text-muted">Exercise #<?= esc($data['exercise']['gazzetted_no'] ?? 'N/A') ?></small>
                                        </td>
                                        <td><?= esc($data['exercise']['advertisement_no'] ?? 'N/A') ?></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?= $data['position_count'] ?> Position<?= $data['position_count'] > 1 ? 's' : '' ?>
                                            </span>
                                        </td>
                                        <td><?= date('M d, Y', strtotime($data['exercise']['publish_date_from'])) ?></td>
                                        <td><?= date('M d, Y', strtotime($data['exercise']['publish_date_to'])) ?></td>
                                        <td>
                                            <a href="<?= base_url('applicant/jobs/' . $data['exercise']['id']) ?>"
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?= $this->section('scripts') ?>
<script>
// View toggle function
function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    const gridBtn = document.getElementById('gridViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'grid') {
        gridView.style.display = 'block';
        tableView.style.display = 'none';
        gridBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        gridView.style.display = 'none';
        tableView.style.display = 'block';
        gridBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}

$(document).ready(function() {
    // Auto-submit form when organization filter changes
    $('#org_id').on('change', function() {
        $(this).closest('form').submit();
    });

    // Add search functionality with Enter key
    $('#search').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            $(this).closest('form').submit();
        }
    });

    // Add loading state to filter button
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Filtering...');
        submitBtn.prop('disabled', true);

        // Re-enable after a short delay (in case of quick response)
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 2000);
    });

    // Highlight search terms in results
    const searchTerm = '<?= esc($searchTerm ?? '') ?>';
    if (searchTerm) {
        $('.card-body, .table tbody').each(function() {
            const content = $(this).html();
            const regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
            const highlighted = content.replace(regex, '<mark>$1</mark>');
            $(this).html(highlighted);
        });
    }

    // Add animation to cards
    $('.card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('animate__animated animate__fadeInUp');
    });
});
</script>

<style>
/* Add some custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.animate__animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.animate__fadeInUp {
    animation-name: fadeInUp;
}

/* Highlight search results */
mark {
    background-color: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
}

/* Hover effects for cards */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Filter section styling */
.card-body .border-top {
    border-color: #dee2e6 !important;
}

/* View toggle buttons */
.btn-group .btn.active {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-group .btn:not(.active) {
    background-color: white;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-group .btn:not(.active):hover {
    background-color: #f8f9fa;
    border-color: #dc3545;
    color: #dc3545;
}

/* Table view styling */
.table-view .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table-view .table td {
    vertical-align: middle;
}

.table-view .table tbody tr:hover {
    background-color: #f8f9fa;
}
</style>
<?= $this->endSection() ?>

<?= $this->endSection() ?>