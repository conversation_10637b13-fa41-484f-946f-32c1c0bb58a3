<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/jobs/' . $exercise['id']) ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercise Positions
                    </a>
                    <h1 class="h3 mb-2">
                        <?= esc($position['designation']) ?>
                        <span class="badge bg-secondary ms-2"><?= esc($position['position_reference']) ?></span>
                    </h1>
                    <?php if (!empty($position['jd_filepath'])): ?>
                        <div class="text-success mb-1">
                            <i class="fas fa-file-pdf me-1"></i> Job Description document available for download
                        </div>
                    <?php endif; ?>
                    <p class="text-muted">
                        <?= esc($organization['name']) ?> | 
                        Exercise #<?= esc($exercise['gazzetted_no']) ?>
                    </p>
                </div>
                <?php if (!empty($organization['orglogo'])): ?>
                    <img src="<?= base_url(str_replace('public/', '', $organization['orglogo'])) ?>" 
                         alt="Organization Logo" 
                         class="img-fluid" 
                         style="max-height: 60px;">
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Position Details -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Position Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <h6 class="text-primary">Classification</h6>
                            <p><?= esc($position['classification']) ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary">Award</h6>
                            <p><?= !empty($position['award']) ? esc($position['award']) : '<span class="text-muted">Not specified</span>' ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary">Location</h6>
                            <p><?= !empty($position['location']) ? esc($position['location']) : (!empty($organization['addlockprov']) ? esc($organization['addlockprov']) : '<span class="text-muted">Not specified</span>') ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary">Annual Salary</h6>
                            <p>K<?= number_format((float)$position['annual_salary'], 2) ?></p>
                        </div>
                    </div>

                    <?php if (!empty($position['qualifications'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary">Qualifications</h6>
                            <p class="mb-0"><?= nl2br(esc($position['qualifications'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($position['knowledge'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary">Knowledge Required</h6>
                            <p class="mb-0"><?= nl2br(esc($position['knowledge'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($position['skills_competencies'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary">Skills & Competencies</h6>
                            <p class="mb-0"><?= nl2br(esc($position['skills_competencies'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($position['job_experiences'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary">Required Experience</h6>
                            <p class="mb-0"><?= nl2br(esc($position['job_experiences'])) ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($position['jd_filepath'])): ?>
                        <div class="mb-4">
                            <h6 class="text-primary">Job Description Document</h6>
                            <p class="mb-0">
                                <a href="<?= base_url(str_replace('public/', '', $position['jd_filepath'])) ?>" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-file-pdf me-2"></i> Download Job Description
                                </a>
                                <span class="text-muted ms-2">View complete position details and requirements</span>
                            </p>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <h6 class="alert-heading">Important Dates</h6>
                        <p class="mb-0">
                            <strong>Published:</strong> <?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?><br>
                            <strong>Closing Date:</strong> <?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Section -->
        <div class="col-lg-4">
            <?php if($has_applied): ?>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-check-circle me-2"></i>Application Submitted
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-1">You have already applied for this position</h6>
                                    <p class="mb-0">Application Number: <strong><?= esc($application['application_number']) ?></strong></p>
                                    <p class="mb-0">Status: 
                                        <span class="badge bg-<?= $application['status'] === 'pending' ? 'warning' : 
                                            ($application['status'] === 'approved' ? 'success' : 
                                            ($application['status'] === 'rejected' ? 'danger' : 'info')) ?>">
                                            <?= ucfirst(esc($application['status'])) ?>
                                        </span>
                                    </p>
                                    <p class="mb-0">Applied on: <?= date('M d, Y', strtotime($application['created_at'])) ?></p>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($application['remarks'])): ?>
                            <div class="alert alert-info">
                                <h6 class="alert-heading">Remarks</h6>
                                <p class="mb-0"><?= nl2br(esc($application['remarks'])) ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="d-grid gap-2">
                            <a href="<?= base_url('applicant/applications') ?>" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>View All Applications
                            </a>
                            <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>Browse Other Positions
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Apply for this Position</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Calculate profile completion percentage based on required fields
                        $required_fields = [
                            'first_name' => 'First Name',
                            'last_name' => 'Last Name',
                            'gender' => 'Gender',
                            'dobirth' => 'Date of Birth',
                            'contact_details' => 'Contact Number',
                            'email' => 'Email Address',
                            'location_address' => 'Current Address',
                            'place_of_origin' => 'Place of Origin',
                            'citizenship' => 'Citizenship',
                            'id_numbers' => 'ID Numbers (NID, Passport, Driving License)',
                            'current_employer' => 'Current Employer',
                            'current_position' => 'Current Position',
                            'current_salary' => 'Current Salary',
                            'referees' => 'Referees'
                        ];

                        $filled_fields = 0;
                        $missing_fields = [];
                        foreach($required_fields as $field => $label) {
                            if(!empty($applicant[$field])) {
                                $filled_fields++;
                            } else {
                                $missing_fields[] = $label;
                            }
                        }
                        $profile_completion = round(($filled_fields / count($required_fields)) * 100);
                        ?>

                        <?php if($profile_completion < 100): ?>
                            <div class="alert alert-warning">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">Profile Incomplete</h6>
                                        <p class="mb-0">Your profile is <?= $profile_completion ?>% complete. Please complete the following required information to apply for jobs:</p>
                                    </div>
                                </div>

                                <?php if (!empty($missing_fields)): ?>
                                    <div class="mb-3">
                                        <h6 class="text-danger mb-2">Missing Information:</h6>
                                        <ul class="list-unstyled mb-0">
                                            <?php foreach ($missing_fields as $field): ?>
                                                <li class="mb-1">
                                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                                    <?= esc($field) ?>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-warning" role="progressbar"
                                         style="width: <?= $profile_completion ?>%;"
                                         aria-valuenow="<?= $profile_completion ?>"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <a href="<?= base_url('applicant/profile') ?>" class="btn btn-warning w-100">
                                    <i class="fas fa-user-edit me-2"></i>Complete Your Profile
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($position['jd_filepath'])): ?>
                            <div class="alert alert-light border mb-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="fas fa-file-pdf text-danger fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Job Description Available</h6>
                                        <p class="mb-2 small">We recommend reviewing the complete job description before applying</p>
                                        <a href="<?= base_url(str_replace('public/', '', $position['jd_filepath'])) ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-download me-1"></i> Download JD
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <form action="<?= base_url('applicant/jobs/apply/' . $position['id']) ?>"
                              method="post"
                              id="applicationForm">
                            <?= csrf_field() ?>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>Application Submission
                                </h6>
                                <p class="mb-0">
                                    By clicking "Submit Application", your profile information, work experience, and education records will be automatically included in your application for this position.
                                </p>
                            </div>

                            <div class="d-grid">
                                <?php if($profile_completion == 100): ?>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Application
                                    </button>
                                <?php else: ?>
                                    <button type="button" class="btn btn-secondary" disabled>
                                        <i class="fas fa-lock me-2"></i>Complete Profile to Apply
                                    </button>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Handle form submission
    $('#applicationForm').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Submitting...').prop('disabled', true);

        // Submit form using standard form submission
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    setTimeout(() => {
                        window.location.href = '<?= base_url('applicant/applications') ?>';
                    }, 2000);
                } else {
                    toastr.error(response.message || 'Error submitting application');
                    submitBtn.html(originalText).prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                toastr.error('Error submitting application: ' + error);
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 