TITLE: Querying GenAIAqa Class Dependencies
DESCRIPTION: This final example query asks about the dependencies of the `GenAIAqa` class. It further illustrates the power of the RetrievalQA chain in providing detailed information about code components, such as their required dependencies, by leveraging the indexed code context.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/langchain/Code_analysis_using_Gemini_LangChain_and_DeepLake.ipynb#_snippet_18

LANGUAGE: python
CODE:
```
call_qa_chain("What are the dependencies of the GenAIAqa class?")
```

----------------------------------------

TITLE: Calling Gemini API with Previous Function Response (Bash)
DESCRIPTION: This example demonstrates how to make a multi-turn call to the Gemini 2.0 Flash API using `curl`. It includes a user query, a model's function call, and a subsequent function response, simulating a conversation where the model uses a tool (`find_theaters`) to answer a user's request. The request also declares available tools like `find_movies`, `find_theaters`, and `get_showtimes` for the model to use.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/rest/Function_calling_REST.ipynb#_snippet_3

LANGUAGE: json
CODE:
```
"functionCall": {
  "name": "find_theaters",
  "args": {
    "movie": "Barbie",
    "location": "Mountain View, CA"
  }
}
```

LANGUAGE: bash
CODE:
```
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$GOOGLE_API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{
    "contents": [{
      "role": "user",
      "parts": [{
        "text": "Which theaters in Mountain View show Barbie movie?"
    }]
  }, {
    "role": "model",
    "parts": [{
      "functionCall": {
        "name": "find_theaters",
        "args": {
          "location": "Mountain View, CA",
          "movie": "Barbie"
        }
      }
    }]
  }, {
    "role": "function",
    "parts": [{
      "functionResponse": {
        "name": "find_theaters",
        "response": {
          "name": "find_theaters",
          "content": {
            "movie": "Barbie",
            "theaters": [{
              "name": "AMC Mountain View 16",
              "address": "2000 W El Camino Real, Mountain View, CA 94040"
            }, {
              "name": "Regal Edwards 14",
              "address": "245 Castro St, Mountain View, CA 94040"
            }]
          }
        }
      }
    }]
  }],
  "tools": [{
    "functionDeclarations": [{
      "name": "find_movies",
      "description": "find movie titles currently playing in theaters based on any description, genre, title words, etc.",
      "parameters": {
        "type": "OBJECT",
        "properties": {
          "location": {
            "type": "STRING",
            "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
          },
          "description": {
            "type": "STRING",
            "description": "Any kind of description including category or genre, title words, attributes, etc."
          }
        },
        "required": ["description"]
      }
    }, {
      "name": "find_theaters",
      "description": "find theaters based on location and optionally movie title which are is currently playing in theaters",
      "parameters": {
        "type": "OBJECT",
        "properties": {
          "location": {
            "type": "STRING",
            "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
          },
          "movie": {
            "type": "STRING",
            "description": "Any movie title"
          }
        },
        "required": ["location"]
      }
    }, {
      "name": "get_showtimes",
      "description": "Find the start times for movies playing in a specific theater",
      "parameters": {
        "type": "OBJECT",
        "properties": {
          "location": {
            "type": "STRING",
            "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
          },
          "movie": {
            "type": "STRING",
            "description": "Any movie title"
          },
          "theater": {
            "type": "STRING",
            "description": "Name of the theater"
          },
          "date": {
            "type": "STRING",
            "description": "Date for requested showtime"
          }
        },
        "required": ["location", "movie", "theater", "date"]
      }
    }]
  }]
}' 2> /dev/null
```

----------------------------------------

TITLE: Installing Google Generative AI Library in Python
DESCRIPTION: This command installs or upgrades the `google-genai` library, which is essential for interacting with the Gemini API. The `-U` flag ensures an upgrade to the latest version, and `-q` suppresses output for a quiet installation.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/prompting/Basic_Classification.ipynb#_snippet_1

LANGUAGE: Python
CODE:
```
%pip install -U -q "google-genai"
```

----------------------------------------

TITLE: Installing Google GenAI Library - Python
DESCRIPTION: This command installs or upgrades the `google-genai` library, which is essential for interacting with the Google Gemini API. The `-U` flag ensures an upgrade if already installed, and `-q` suppresses output, making the installation quieter.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/prompting/Adding_context_information.ipynb#_snippet_1

LANGUAGE: Python
CODE:
```
%pip install -U -q "google-genai>=1.0.0"
```

----------------------------------------

TITLE: Executing Sequential Functions with Compositional Function Calling (Python)
DESCRIPTION: This snippet demonstrates compositional function calling, where the model combines user-defined functions (`turn_on_the_lights`, `turn_off_the_lights`) with the `code_execution` tool to perform a sequence of actions. The model will generate code to execute these functions with a specified delay, pausing for responses after each call. It uses `TEXT` modality for interaction.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Get_started_LiveAPI_tools.ipynb#_snippet_15

LANGUAGE: python
CODE:
```
prompt="Can you turn on the lights wait 10s and then turn them off?"

tools = [
    {'code_execution': {}},
    {'function_declarations': [turn_on_the_lights, turn_off_the_lights]}
]

await run(prompt, tools=tools, modality="TEXT")
```

----------------------------------------

TITLE: Looking Up Phrase on Wikipedia Page with ReAct in Python
DESCRIPTION: This method searches for a specific phrase within the content of the most recently searched Wikipedia page. It extracts a contextual snippet around the phrase, with the length controlled by `context_length`, and prints the source URL.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Search_Wikipedia_using_ReAct.ipynb#_snippet_10

LANGUAGE: Python
CODE:
```
@ReAct.add_method
def lookup(self, phrase: str, context_length=200):
    """Searches for the `phrase` in the lastest Wikipedia search page
    and returns number of sentences which is controlled by the
    `context_length` parameter.

    Args:
        phrase: Lookup phrase to search for within a page. Generally
        attributes to some specification of any topic.

        context_length: Number of words to consider
        while looking for the answer.

    Returns:
        result: Context related to the `phrase` within the page.
    """
    # get the last searched Wikipedia page and find `phrase` in it.
    page = wikipedia.page(self._search_history[-1], auto_suggest=False)
    page = page.content
    page = self.clean(page)
    start_index = page.find(phrase)

    # extract sentences considering the context length defined
    result = page[max(0, start_index - context_length):start_index+len(phrase)+context_length]
    print(f"Information Source: {self._search_urls[-1]}")
    return result
```

----------------------------------------

TITLE: Calling Functions with Gemini Live API (Python)
DESCRIPTION: This example demonstrates how to use the `run` function to trigger a function call via the Gemini Live API. It sets up a prompt that implies a function call ('Turn on the lights') and provides a `tools` configuration containing the `function_declarations` for `turn_on_the_lights` and `turn_off_the_lights`.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Get_started_LiveAPI_tools.ipynb#_snippet_13

LANGUAGE: Python
CODE:
```
prompt = "Turn on the lights"

tools = [
    {'function_declarations': [turn_on_the_lights, turn_off_the_lights]}
]

await run(prompt, tools=tools, modality = "TEXT")
```

----------------------------------------

TITLE: Streaming Content Generation Synchronously with Gemini API in Python
DESCRIPTION: This snippet demonstrates how to stream content from the Gemini API synchronously using `generate_content_stream`. It iterates over the response chunks, printing each part of the generated text as it becomes available.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Streaming.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
for chunk in client.models.generate_content_stream(
  model='gemini-2.0-flash',
  contents='Tell me a story in 300 words.'
):
    print(chunk.text)
    print("_" * 80)
```

----------------------------------------

TITLE: Generating Company Report with Gemini 2.0 and Search Tool
DESCRIPTION: This comprehensive Python snippet demonstrates how to use Gemini 2.0 with the integrated search tool to generate a company research report. It sets up a system instruction for the AI, configures the `GenerateContentConfig` to enable `google_search` as a tool, and then streams the content generation. The code also parses the streamed response to extract the final report and display search suggestions.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Search_grounding_for_research_report.ipynb#_snippet_7

LANGUAGE: Python
CODE:
```
sys_instruction = """You are an analyst that conducts company research.\nYou are given a company name, and you will work on a company report. You have access\nto Google Search to look up company news, updates and metrics to write research reports.\n\nWhen given a company name, identify key aspects to research, look up that information\nand then write a concise company report.\n\nFeel free to plan your work and talk about it, but when you start writing the report,\nput a line of dashes (---) to demarcate the report itself, and say nothing else after\nthe report has finished.\n"""

config = GenerateContentConfig(system_instruction=sys_instruction, tools=[Tool(google_search={})], temperature=0)
response_stream = client.models.generate_content_stream(
    model=MODEL, config=config, contents=[COMPANY])

report = io.StringIO()
for chunk in response_stream:
  candidate = chunk.candidates[0]

  for part in candidate.content.parts:
    if part.text:
      display(Markdown(part.text))

      # Find and save the report itself.
      if m := re.search('(^|\n)-+\n(.*)$', part.text, re.M):
          # Find the starting '---' line and start saving.
          report.write(m.group(2))
      elif report.tell():
        # If there's already something in the buffer, keep recording.
        report.write(part.text)

    else:
      print(json.dumps(part.model_dump(exclude_none=True), indent=2))

  # You must enable Google Search Suggestions
  if gm := candidate.grounding_metadata:
    if sep := gm.search_entry_point:
      display(HTML(sep.rendered_content))
```

----------------------------------------

TITLE: Single-Turn Function Calling with Gemini API using cURL
DESCRIPTION: This cURL command demonstrates a single-turn function calling scenario with the Gemini API. It sends a user query about movie showtimes along with multiple function declarations (find_movies, find_theaters, get_showtimes) to the gemini-2.0-flash model. The model is expected to return a function call based on the query and declared tools.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/rest/Function_calling_REST.ipynb#_snippet_2

LANGUAGE: bash
CODE:
```
%%bash

curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$GOOGLE_API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{
    "contents": {
      "role": "user",
      "parts": {
        "text": "Which theaters in Mountain View show Barbie movie?"
    }
  },
  "tools": [
    {
      "function_declarations": [
        {
          "name": "find_movies",
          "description": "find movie titles currently playing in theaters based on any description, genre, title words, etc.",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
              },
              "description": {
                "type": "string",
                "description": "Any kind of description including category or genre, title words, attributes, etc."
              }
            },
            "required": [
              "description"
            ]
          }
        },
        {
          "name": "find_theaters",
          "description": "find theaters based on location and optionally movie title which are is currently playing in theaters",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
              },
              "movie": {
                "type": "string",
                "description": "Any movie title"
              }
            },
            "required": [
              "location"
            ]
          }
        },
        {
          "name": "get_showtimes",
          "description": "Find the start times for movies playing in a specific theater",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "The city and state, e.g. San Francisco, CA or a zip code e.g. 95616"
              },
              "movie": {
                "type": "string",
                "description": "Any movie title"
              },
              "theater": {
                "type": "string",
                "description": "Name of the theater"
              },
              "date": {
                "type": "string",
                "description": "Date for requested showtime"
              }
            },
            "required": [
              "location",
              "movie",
              "theater",
              "date"
            ]
          }
        }
      ]
    }
  ]
}' 2> /dev/null
```

----------------------------------------

TITLE: Extracting Invoice Data with Pydantic and Gemini API
DESCRIPTION: This code defines `Item` and `Invoice` Pydantic models to structure invoice data, including items, quantities, and total gross worth. It then uses the `extract_structured_data` function to process `invoice.pdf`, demonstrating how to extract and print specific fields and iterate through extracted items.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Pdf_structured_outputs_on_invoices_and_forms.ipynb#_snippet_8

LANGUAGE: Python
CODE:
```
from pydantic import BaseModel, Field

class Item(BaseModel):
    description: str = Field(description="The description of the item")
    quantity: float = Field(description="The Qty of the item")
    gross_worth: float = Field(description="The gross worth of the item")

class Invoice(BaseModel):
    """Extract the invoice number, date and all list items with description, quantity and gross worth and the total gross worth."""
    invoice_number: str = Field(description="The invoice number e.g. 1234567890")
    date: str = Field(description="The date of the invoice e.g. 2024-01-01")
    items: list[Item] = Field(description="The list of items with description, quantity and gross worth")
    total_gross_worth: float = Field(description="The total gross worth of the invoice")


result = extract_structured_data("invoice.pdf", Invoice)
print(type(result))
print(f"Extracted Invoice: {result.invoice_number} on {result.date} with total gross worth {result.total_gross_worth}")
for item in result.items:
    print(f"Item: {item.description} with quantity {item.quantity} and gross worth {item.gross_worth}")
```

----------------------------------------

TITLE: Generating JSON Content with Gemini API in Python (Schema-based)
DESCRIPTION: This snippet demonstrates generating content by directly providing a Python type hint (`list[Recipe]`) to the `response_schema` parameter in the `config`. This method ensures the model's output strictly adheres to the defined `Recipe` schema, along with setting the `response_mime_type` to `application/json`.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/JSON_mode.ipynb#_snippet_8

LANGUAGE: python
CODE:
```
result = client.models.generate_content(
    model=MODEL_ID,
    contents="List a few imaginative cookie recipes along with a one-sentence description as if you were a gourmet restaurant and their main ingredients",
    config={
        'response_mime_type': 'application/json',
        'response_schema': list[Recipe]
    }
)
```

----------------------------------------

TITLE: Building a RAG Chain with LangChain Expression Language (Python)
DESCRIPTION: This snippet constructs a 'stuff documents chain' using LangChain Expression Language (LCEL) for a Retrieval Augmented Generation (RAG) application. It defines a `format_docs` helper function and then chains together a retriever, a `RunnablePassthrough` for the question, the previously defined `llm_prompt`, the `llm` model, and a `StrOutputParser` to process and structure the LLM's response.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/langchain/Gemini_LangChain_QA_Chroma_WebLoad.ipynb#_snippet_11

LANGUAGE: python
CODE:
```
# Combine data from documents to readable string format.
def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)

# Create stuff documents chain using LCEL.
#
# This is called a chain because you are chaining together different elements
# with the LLM. In the following example, to create the stuff chain, you will
# combine the relevant context from the website data matching the question, the
# LLM model, and the output parser together like a chain using LCEL.
#
# The chain implements the following pipeline:
# 1. Extract the website data relevant to the question from the Chroma
#    vector store and save it to the variable `context`.
# 2. `RunnablePassthrough` option to provide `question` when invoking
#    the chain.
# 3. The `context` and `question` are then passed to the prompt where they
#    are populated in the respective variables.
# 4. This prompt is then passed to the LLM (`gemini-2.0-flash`).
# 5. Output from the LLM is passed through an output parser
#    to structure the model's response.
rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | llm_prompt
    | llm
    | StrOutputParser()
)
```

----------------------------------------

TITLE: Initiating Parallel Function Calls in Chat
DESCRIPTION: Configures a `ChatSession` to enable parallel function calling by setting `tool_config` with `function_calling_config.mode` to 'any'. It then sends a message that prompts the model to utilize multiple defined tools simultaneously, demonstrating the Gemini API's capability to handle independent function calls in a single turn.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Function_calling.ipynb#_snippet_16

LANGUAGE: Python
CODE:
```
# You generally set "mode": "any" to make sure Gemini actually *uses* the given tools.
party_chat = client.chats.create(
    model=MODEL_ID,
    config={
        "tools": house_fns,
        "tool_config" : {
            "function_calling_config": {
                "mode": "any"
            }
        }
    }
)

# Call the API
response = party_chat.send_message(
    "Turn this place into a party!"
)


print_history(party_chat)
```

----------------------------------------

TITLE: Sending Prompts and Receiving Replies with Gemini Live API (Python)
DESCRIPTION: This asynchronous function establishes a connection to the Gemini Live API, sends a user prompt, and processes the single-turn response. It supports both text and audio modalities and handles various response types including text, audio data, server content, and tool calls. The `modality` parameter can be set to 'AUDIO' for spoken output.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Get_started_LiveAPI_tools.ipynb#_snippet_8

LANGUAGE: Python
CODE:
```
n = 0
async def run(prompt, modality="TEXT", tools=None):
  global n
  if tools is None:
    tools=[]

  config = {
          "tools": tools,
          "response_modalities": [modality]
  }

  async with client.aio.live.connect(model=model_name, config=config) as session:
    display.display(display.Markdown(prompt))
    display.display(display.Markdown('-------------------------------'))
    await session.send_client_content(
      turns={"role": "user", "parts": [{"text": prompt}]}, turn_complete=True
    )

    audio = False
    filename = f'audio_{n}.wav'
    with wave_file(filename) as wf:
      async for response in session.receive():
        logger.debug(str(response))
        if text:=response.text:
          display.display(display.Markdown(text))
          continue

        if data:=response.data:
          print('.', end='')
          wf.writeframes(data)
          audio = True
          continue

        server_content = response.server_content
        if server_content is not None:
          handle_server_content(wf, server_content)
          continue

        tool_call = response.tool_call
        if tool_call is not None:
          await handle_tool_call(session, tool_call)


  if audio:
    display.display(display.Audio(filename, autoplay=True))
    n = n+1
```

----------------------------------------

TITLE: Configuring Gemini API Key (Python)
DESCRIPTION: This snippet retrieves the `GOOGLE_API_KEY` from Colab user data and initializes the `google.genai` client. This is a prerequisite for making any subsequent calls to the Gemini API, ensuring proper authentication.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/json_capabilities/Entity_Extraction_JSON.ipynb#_snippet_1

LANGUAGE: python
CODE:
```
from google.colab import userdata
from google import genai

GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=GOOGLE_API_KEY)
```

----------------------------------------

TITLE: Configuring Gemini API Client (Python)
DESCRIPTION: This snippet retrieves the Google API key from Colab user data secrets and initializes the `genai.Client` object. This client is essential for making authenticated requests to the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/prompting/Self_ask_prompting.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
from google.colab import userdata
from google import genai

GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=GOOGLE_API_KEY)
```

----------------------------------------

TITLE: Configuring Gemini API Client with Colab Secret (Python)
DESCRIPTION: This code imports the necessary `genai` module and retrieves the `GOOGLE_API_KEY` from Colab's `userdata` secrets. It then initializes the `genai.Client` with this key, establishing the connection required to make calls to the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Entity_Extraction.ipynb#_snippet_2

LANGUAGE: Python
CODE:
```
from google import genai
from google.colab import userdata

GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=GOOGLE_API_KEY)
```

----------------------------------------

TITLE: Initializing Gemini API Client in Colab
DESCRIPTION: This snippet imports necessary modules and initializes the `genai.Client` using an API key retrieved from Colab's `userdata` secrets. This client object is essential for making subsequent API calls to the Gemini model for content generation and chat interactions.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/System_instructions.ipynb#_snippet_2

LANGUAGE: Python
CODE:
```
from google.colab import userdata
from google import genai
from google.genai import types

client = genai.Client(api_key=userdata.get("GOOGLE_API_KEY"))
```

----------------------------------------

TITLE: Customizing Video Analysis FPS with Gemini API (Python)
DESCRIPTION: This snippet demonstrates how to customize the frames per second (FPS) for video analysis using the Gemini API. It sets a higher FPS (24) for a specific interval of a Nascar pit-stop video to capture more detail in fast-changing visuals and asks a specific question about tire changes.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Video_understanding.ipynb#_snippet_16

LANGUAGE: Python
CODE:
```
response = client.models.generate_content(
    model=MODEL_ID,
    contents=types.Content(
        parts=[
            types.Part(
                file_data=types.FileData(file_uri='https://www.youtube.com/watch?v=McN0-DpyHzE'),
                video_metadata=types.VideoMetadata(
                    start_offset='15s',
                    end_offset='35s',
                    fps=24
                )
            ),
            types.Part(text='How many tires where changed? Front tires or rear tires?')
        ]
    )
)

Markdown(response.text)
```

----------------------------------------

TITLE: Installing Google Generative AI Library - Python
DESCRIPTION: This command installs the `google-generativeai` Python library, which is essential for interacting with Google's Gemini models. The `-q` flag ensures a quiet installation, and the version constraint `>=0.7.2` specifies a minimum required version for compatibility.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Search_Wikipedia_using_ReAct.ipynb#_snippet_1

LANGUAGE: python
CODE:
```
%pip install -q "google-generativeai>=0.7.2"
```

----------------------------------------

TITLE: Generating JSON Content with Gemini API in Python (Prompt-based)
DESCRIPTION: This snippet selects a specific Gemini model and then calls the `generate_content` method. It passes the previously defined `prompt` and configures the `response_mime_type` to `application/json` to ensure the model's output is formatted as JSON, based on the schema described in the prompt.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/JSON_mode.ipynb#_snippet_4

LANGUAGE: python
CODE:
```
MODEL_ID = "gemini-2.5-flash-preview-05-20" # @param ["gemini-2.0-flash-lite","gemini-2.0-flash","gemini-2.5-flash-preview-05-20","gemini-2.5-pro-preview-05-06"] {"allow-input":true, isTemplate: true}

raw_response = client.models.generate_content(
    model=MODEL_ID,
    contents=prompt,
    config={
        'response_mime_type': 'application/json'
    }
)
```

----------------------------------------

TITLE: Analyzing YouTube Videos with Gemini API (Python)
DESCRIPTION: This snippet illustrates how to analyze a YouTube video using the Gemini API. It sends a specific query to the model to find all instances of a particular word ("AI") spoken by Sundar, providing timestamps and broader context for each occurrence.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Video_understanding.ipynb#_snippet_13

LANGUAGE: Python
CODE:
```
response = client.models.generate_content(
    model=MODEL_ID,
    contents=types.Content(
        parts=[
            types.Part(text="Find all the instances where Sundar says \"AI\". Provide timestamps and broader context for each instance."),
            types.Part(
                file_data=types.FileData(file_uri='https://www.youtube.com/watch?v=ixRanV-rdAQ')
            )
        ]
    )
)

Markdown(response.text)
```

----------------------------------------

TITLE: Summarizing Specific Intervals of YouTube Videos with Gemini API (Python)
DESCRIPTION: This snippet demonstrates how to analyze a specific time segment of a YouTube video using clipping intervals. It sends a request to the Gemini API to summarize a defined portion (20 minutes 50 seconds to 26 minutes 10 seconds) of a Google I/O keynote video.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Video_understanding.ipynb#_snippet_14

LANGUAGE: Python
CODE:
```
response = client.models.generate_content(
    model=MODEL_ID,
    contents=types.Content(
        parts=[
            types.Part(
                file_data=types.FileData(file_uri='https://www.youtube.com/watch?v=XEzRZ35urlk'),
                video_metadata=types.VideoMetadata(
                    start_offset='1250s',
                    end_offset='1570s'
                )
            ),
            types.Part(text='Please summarize the video in 3 sentences.')
        ]
    )
)

Markdown(response.text)
```

----------------------------------------

TITLE: Extracting Entities into Structured Lists (Python)
DESCRIPTION: This code refines the entity extraction by explicitly requesting the Gemini model to return the extracted street names and forms of transport as two distinct Python-like lists. It demonstrates how to control the output format for structured data retrieval.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Entity_Extraction.ipynb#_snippet_6

LANGUAGE: Python
CODE:
```
directions_list_prompt = f"""
  From the given text, extract the following entities and
  return a list of them.
  Entities to extract: street name, form of transport.
  Text: {directions}
  Return your answer as two lists:
  Street = [street names]
  Transport = [forms of transport]
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=directions_list_prompt
)

Markdown(response.text)
```

----------------------------------------

TITLE: Structuring Information from Video with Gemini in Python
DESCRIPTION: This Python snippet showcases Gemini's capability to reason about and structure information from real-world objects in a video. It prompts the model to create a table of items and notes from the `pottery_video`, including a system instruction to escape dollar signs, demonstrating advanced content generation and formatting control.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Video_understanding.ipynb#_snippet_11

LANGUAGE: python
CODE:
```
prompt = "Give me a table of my items and notes" # @param ["Give me a table of my items and notes", "Help me come up with a selling pitch for my potteries"] {"allow-input":true}

video = pottery_video # @param ["trailcam_video", "pottery_video", "post_its_video", "user_study_video"] {"type":"raw","allow-input":true}

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        video,
        prompt,
    ],
    config = types.GenerateContentConfig(
        system_instruction="Don't forget to escape the dollar signs",
    )
)

Markdown(response.text)
```

----------------------------------------

TITLE: Initializing Google Gen AI Client in Python
DESCRIPTION: This code initializes the Google Gen AI client, which will automatically pick up the API key from the environment variable. This client object is the primary interface for interacting with the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Get_started_LiveAPI_tools.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
from google import genai

client = genai.Client()
```

----------------------------------------

TITLE: Setting Gemini API Key Environment Variable - Python
DESCRIPTION: This code sets the `GOOGLE_API_KEY` environment variable using a key retrieved from Colab's user data. This is a prerequisite for authenticating requests to the Gemini API, ensuring the key is securely accessed and available to subsequent API calls.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/rest/System_instructions_REST.ipynb#_snippet_1

LANGUAGE: python
CODE:
```
os.environ['GOOGLE_API_KEY'] = userdata.get('GOOGLE_API_KEY')
```

----------------------------------------

TITLE: Setting Google API Key Environment Variable (Python)
DESCRIPTION: This code sets the 'GOOGLE_API_KEY' environment variable using a key retrieved from Google Colab's userdata secrets. This ensures that the API key is securely loaded and available for authentication when making API calls.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/rest/Safety_REST.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
os.environ['GOOGLE_API_KEY'] = userdata.get('GOOGLE_API_KEY')
```

----------------------------------------

TITLE: Configuring Google API Key for Gemini
DESCRIPTION: This Python code retrieves the Google API key from Colab user data secrets and sets it as an environment variable. This is a prerequisite for authenticating requests to the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/llamaindex/Gemini_LlamaIndex_QA_Chroma_WebPageReader.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
import os
from google.colab import userdata
GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')

os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
```

----------------------------------------

TITLE: Sending Message with ANY Mode Function Calling in Python
DESCRIPTION: This Python snippet demonstrates how to initiate a chat session with the Google Gemini API and send a message using 'ANY' mode for function calling. It configures the model to use provided tools, specifically forcing at least one function call, and enables automatic function execution for a single remote call. The `system_instruction` and `light_controls` are prerequisites for this configuration.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Function_calling.ipynb#_snippet_20

LANGUAGE: python
CODE:
```
chat = client.chats.create(model=MODEL_ID)

response = chat.send_message(
    "Make this place PURPLE!",
    config={
        "system_instruction": instruction,
        "tools": light_controls, # Provide all tools
        "tool_config" : {
            "function_calling_config": {
                "mode": "any"
            }
        },
        "automatic_function_calling": {
            "maximum_remote_calls" : 1
        }
      } # But restrict to available_fns with ANY mode
)

print_history(chat)
```

----------------------------------------

TITLE: Defining Sentiment Analysis Schema and System Instruction
DESCRIPTION: This code defines Python `enum` and `TypedDict` classes to structure the expected JSON output for sentiment scores (positive, negative, neutral) with magnitude (weak/strong). It also sets a `system_instruct` string for the Gemini model, guiding it to generate sentiment probabilities for the entire input text.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/json_capabilities/Sentiment_Analysis.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
import enum
from typing_extensions import TypedDict


class Magnitude(enum.Enum):
  WEAK = "weak"
  STRONG = "strong"


class Sentiment(TypedDict):
  positive_sentiment_score: Magnitude
  negative_sentiment_score: Magnitude
  neutral_sentiment_score: Magnitude


system_instruct = """
Generate each sentiment score probability (positive, negative, or neutral) for the whole text.
"""
```

----------------------------------------

TITLE: Creating a ChromaDB Vector Database
DESCRIPTION: This function 'create_chroma_db' initializes a ChromaDB client and creates a new collection with a specified 'name'. It uses the 'GeminiEmbeddingFunction' to generate embeddings for the provided 'documents' and adds each document along with a unique ID to the collection. The function returns the populated ChromaDB collection object.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/chromadb/Vectordb_with_chroma.ipynb#_snippet_7

LANGUAGE: python
CODE:
```
def create_chroma_db(documents, name):
  chroma_client = chromadb.Client()
  db = chroma_client.create_collection(
      name=name,
      embedding_function=GeminiEmbeddingFunction()
  )

  for i, d in enumerate(documents):
    db.add(
      documents=d,
      ids=str(i)
    )
  return db
```

----------------------------------------

TITLE: Continuing Conversation with Function Response
DESCRIPTION: Shows how to construct the message history for the Gemini API, including the user's original query, the model's `function_call` response, and the `tool`'s response (the result of the executed function). This complete history is then passed to a subsequent `generate_content` call to allow the model to generate a final, text-based answer based on the function's output.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Function_calling.ipynb#_snippet_14

LANGUAGE: Python
CODE:
```
from google.genai import types
# Build the message history
messages = [
    types.Content(
        role="user",
        parts=[
            types.Part(
                text="Which theaters in Mountain View show the Barbie movie?."
            )
        ]
    ),
    types.Content(
        role="model",
        parts=[part]
    ),
    types.Content(
        role="tool",
        parts=[
            types.Part.from_function_response(
                name=part.function_call.name,
                response={"output":result},
            )
        ]
    )
]

# Generate the next response
response = client.models.generate_content(
    model=MODEL_ID,
    contents=messages,
    config = {
        "tools": theater_functions,
        "automatic_function_calling": {"disable": True}
    }
)
print(response.text)
```

----------------------------------------

TITLE: Making Authenticated REST Calls to Gemini API (Python)
DESCRIPTION: This Python code demonstrates how to make an OAuth authenticated REST API call to the Gemini API. It retrieves an access token using `gcloud auth application-default print-access-token`, constructs HTTP headers with the bearer token, and then makes a GET request to list available models, parsing and printing their names.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Authentication_with_OAuth.ipynb#_snippet_5

LANGUAGE: python
CODE:
```
import requests

access_token = !gcloud auth application-default print-access-token

headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {access_token[0]}',
}

response = requests.get('https://generativelanguage.googleapis.com/v1/models', headers=headers)
response_json = response.json()

# All the model names
for model in response_json['models']:
    print(model['name'])
```

----------------------------------------

TITLE: Configuring Gemini API for Forced Function Calling (ANY Mode) - Bash
DESCRIPTION: This snippet shows how to force the Gemini API to make a function call by setting the `function_calling_config` mode to `any`. It also restricts the model to only call `set_light_color` or `stop_lights` functions using `allowed_function_names`, ensuring specific actions are taken when the lights are already on.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/rest/Function_calling_config_REST.ipynb#_snippet_5

LANGUAGE: bash
CODE:
```
%%bash
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$GOOGLE_API_KEY" \
  -H 'Content-Type: application/json' \
  -d @<(echo '
  {
    "system_instruction": {
      "parts": {
        "text": "You are a helpful lighting system bot. You can turn lights on and off, and you can set the color. Do not perform any other tasks."
      }
    },
    "tools": [' $(cat tools.json) '],

    "tool_config": {
      "function_calling_config": {
        "mode": "any",
        "allowed_function_names": ["set_light_color", "stop_lights"]
      }
    },

    "contents": {
      "role": "user",
      "parts": {
        "text": "Make this place PURPLE!"
      }
    }
  }
') 2>/dev/null |sed -n '/"content"/,/"finishReason"/p'
```

----------------------------------------

TITLE: Importing Google Generative AI Package
DESCRIPTION: Imports the `google.generativeai` package, aliased as `genai`, which provides the necessary classes and functions to interact with Google's generative AI models.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/New_in_002.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
import google.generativeai as genai
```

----------------------------------------

TITLE: Installing Google GenAI Library in Python
DESCRIPTION: Installs the `google-genai` Python client library, which is essential for interacting with the Gemini API. The `-U` flag ensures the package is upgraded to the latest version, and `-q` suppresses installation output.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Talk_to_documents_with_embeddings.ipynb#_snippet_0

LANGUAGE: python
CODE:
```
%pip install -U -q "google-genai>=1.0.0"
```

----------------------------------------

TITLE: Installing Google Generative AI SDK in Python
DESCRIPTION: This snippet installs the `google-genai` Python SDK using pip, ensuring the latest version is installed quietly. It is a prerequisite for interacting with the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Streaming.ipynb#_snippet_0

LANGUAGE: python
CODE:
```
%pip install -U -q "google-genai"
```

----------------------------------------

TITLE: Installing Google GenAI and TQDM Libraries (Python)
DESCRIPTION: This command installs or upgrades the `google-genai` library (version 1.0.0 or newer) for interacting with the Gemini API and `tqdm` for progress bars. The `-U` flag ensures upgrade, and `-q` suppresses output.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Translate_a_Public_Domain_Book.ipynb#_snippet_1

LANGUAGE: Python
CODE:
```
%pip install -U -q "google-genai>=1.0.0" tqdm
```

----------------------------------------

TITLE: Configuring Google API Key - Python
DESCRIPTION: This snippet retrieves the Google API key from Colab user data secrets and sets it as an environment variable. This step is mandatory for authenticating requests to the Gemini API, allowing the application to interact with Google's generative AI models.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/langchain/Gemini_LangChain_QA_Chroma_WebLoad.ipynb#_snippet_3

LANGUAGE: Python
CODE:
```
import os
from google.colab import userdata
GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')

os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
```

----------------------------------------

TITLE: Configuring Gemini API Key
DESCRIPTION: Retrieves the Google API key from Colab user data secrets and configures the `genai` library with it. This step is essential for authenticating requests to the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Tuning.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
from google.colab import userdata
genai.configure(api_key=userdata.get('GOOGLE_API_KEY'))
```

----------------------------------------

TITLE: Setting Up Google Gemini API Client - Python
DESCRIPTION: This code retrieves the Google API key from Colab user data secrets and initializes the `google.genai` client. This setup is a crucial prerequisite for making any subsequent API calls to the Gemini service, ensuring authenticated access.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Apollo_11.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
from google.colab import userdata
from google import genai

GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=GOOGLE_API_KEY)
```

----------------------------------------

TITLE: Setting Google API Key from Colab Secrets in Python
DESCRIPTION: This snippet retrieves the Google API key from Colab's user data secrets and sets it as an environment variable. This is a crucial step for authenticating subsequent API calls to the Gemini service.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/quickstarts/Get_started_LiveAPI_tools.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
from google.colab import userdata
import os

os.environ['GOOGLE_API_KEY']=userdata.get('GOOGLE_API_KEY')
```

----------------------------------------

TITLE: Configuring Gemini API Client - Python
DESCRIPTION: This snippet imports necessary modules and initializes the Gemini API client. It retrieves the API key from Colab's user data secrets, ensuring secure handling of credentials, and then uses it to create a `genai.Client` instance for subsequent API calls.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/Analyze_a_Video_Historic_Event_Recognition.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
from google import genai
from google.colab import userdata

API_KEY = userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=API_KEY)
```

----------------------------------------

TITLE: Configuring Gemini API Key
DESCRIPTION: This snippet retrieves the `GOOGLE_API_KEY` from Colab user data and initializes the `genai.Client`. This client is essential for making authenticated requests to the Gemini API.
SOURCE: https://github.com/google-gemini/cookbook/blob/main/examples/json_capabilities/Text_Summarization.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
from google.colab import userdata
from google import genai

GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')
client = genai.Client(api_key=GOOGLE_API_KEY)
```