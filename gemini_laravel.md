TITLE: Generating Content with Text and Video (PHP)
DESCRIPTION: Shows how to use the Gemini model with both text and a reference to an uploaded video file. It uses `Gemini::generativeModel` and includes the video as an `UploadedFile` object, referencing it by its URI.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_6

LANGUAGE: php
CODE:
```
use Gemini\Data\UploadedFile;
use Gemini\Enums\MimeType;
use Gemini\Laravel\Facades\Gemini;

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->generateContent([
        'What is this video?',
        new UploadedFile(
            fileUri: '123-456', // accepts just the name or the full URI
            mimeType: MimeType::VIDEO_MP4
        )
    ]);

$result->text(); //  The picture shows a table with a white tablecloth. On the table are two cups of coffee, a bowl of blueberries, a silver spoon, and some flowers. There are also some blueberry scones on the table.
```

----------------------------------------

TITLE: Check Types with Composer (Bash)
DESCRIPTION: Specifically runs the type checking tests for the project using Composer. This verifies type hints and ensures type correctness.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/CONTRIBUTING.md#_snippet_3

LANGUAGE: bash
CODE:
```
composer test:types
```

----------------------------------------

TITLE: Generating Text Content with Gemini in Laravel
DESCRIPTION: Demonstrates how to use the `Gemini` Facade in Laravel to interact with a generative model (`gemini-2.0-flash`) and generate text content from a simple input string. It shows how to access the generated text result.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_3

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')->generateContent('Hello');

$result->text(); // Hello! How can I assist you today?
```

----------------------------------------

TITLE: Configuring Gemini Model Parameters and Safety Settings (PHP)
DESCRIPTION: Shows how to create `SafetySetting` objects for different harm categories and thresholds, define a `GenerationConfig` with parameters like `stopSequences`, `maxOutputTokens`, `temperature`, `topP`, and `topK`, and then apply these configurations when calling `generateContent` on a specific model.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_12

LANGUAGE: php
CODE:
```
use Gemini\Data\GenerationConfig;
use Gemini\Enums\HarmBlockThreshold;
use Gemini\Data\SafetySetting;
use Gemini\Enums\HarmCategory;
use Gemini\Laravel\Facades\Gemini;

$safetySettingDangerousContent = new SafetySetting(
    category: HarmCategory::HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold::BLOCK_ONLY_HIGH
);

$safetySettingHateSpeech = new SafetySetting(
    category: HarmCategory::HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold::BLOCK_ONLY_HIGH
);

$generationConfig = new GenerationConfig(
    stopSequences: [
        'Title',
    ],
    maxOutputTokens: 800,
    temperature: 1,
    topP: 0.8,
    topK: 10
);

$generativeModel = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->withSafetySetting($safetySettingDangerousContent)
    ->withSafetySetting($safetySettingHateSpeech)
    ->withGenerationConfig($generationConfig)
    ->generateContent("Write a story about a magic backpack.");
```

----------------------------------------

TITLE: Asserting Gemini Requests in PHP
DESCRIPTION: Provides examples of using assertion methods like `assertSent`, `assertNotSent`, and `assertNothingSent` on the fake Gemini client or specific resources/models to verify which API calls were made during testing. Callbacks can be used for detailed parameter checks.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_18

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;
use Gemini\Resources\GenerativeModel;
use Gemini\Resources\Models;

// assert list models request was sent
Gemini::models()->assertSent(callback: function ($method) {
    return $method === 'list';
});
// or
Gemini::assertSent(resource: Models::class, callback: function ($method) {
    return $method === 'list';
});

Gemini::generativeModel(model: 'gemini-2.0-flash')->assertSent(function (string $method, array $parameters) {
    return $method === 'generateContent' &&
        $parameters[0] === 'Hello';
});
// or
Gemini::assertSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash', callback: function (string $method, array $parameters) {
    return $method === 'generateContent' &&
        $parameters[0] === 'Hello';
});


// assert 2 generative model requests were sent
Gemini::assertSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash', callback: 2);
// or
Gemini::geminiPro()->assertSent(2);

// assert no generative model requests were sent
Gemini::assertNotSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash');
// or
Gemini::geminiPro()->assertNotSent();

// assert no requests were sent
Gemini::assertNothingSent();
```

----------------------------------------

TITLE: Faking Streamed Gemini Response in PHP
DESCRIPTION: Illustrates faking a streamed response for `streamGenerateContent` calls. It uses `Gemini::fake()` with `GenerateContentResponse::fakeStream()` to simulate a streamed response, which can then be iterated over in the test.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_17

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;
use Gemini\Responses\GenerativeModel\GenerateContentResponse;

Gemini::fake([
    GenerateContentResponse::fakeStream(),
]);

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')->streamGenerateContent('Hello');

expect($response->getIterator()->current())
    ->text()->toBe('In the bustling city of Aethelwood, where the cobblestone streets whispered');
```

----------------------------------------

TITLE: Implement Function Calling with Gemini (PHP)
DESCRIPTION: This example illustrates how to enable function calling with the Gemini model. It defines a `Tool` with a `FunctionDeclaration` for an 'addition' function, sets up a chat session, sends a message that triggers the function call, processes the function call response using a helper function, and sends the result back to the model.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_10

LANGUAGE: php
CODE:
```
<?php

use Gemini\Data\Content;
use Gemini\Data\FunctionCall;
use Gemini\Data\FunctionDeclaration;
use Gemini\Data\FunctionResponse;
use Gemini\Data\Part;
use Gemini\Data\Schema;
use Gemini\Data\Tool;
use Gemini\Enums\DataType;
use Gemini\Enums\Role;
use Gemini\Laravel\Facades\Gemini;

function handleFunctionCall(FunctionCall $functionCall): Content
{
    if ($functionCall->name === 'addition') {
        return new Content(
            parts: [
                new Part(
                    functionResponse: new FunctionResponse(
                        name: 'addition',
                        response: ['answer' => $functionCall->args['number1'] + $functionCall->args['number2']],
                    )
                )
            ],
            role: Role::USER
        );
    }

    //Handle other function calls
}

$chat = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->withTool(new Tool(
        functionDeclarations: [
            new FunctionDeclaration(
                name: 'addition',
                description: 'Performs addition',
                parameters: new Schema(
                    type: DataType::OBJECT,
                    properties: [
                        'number1' => new Schema(
                            type: DataType::NUMBER,
                            description: 'First number'
                        ),
                        'number2' => new Schema(
                            type: DataType::NUMBER,
                            description: 'Second number'
                        ),
                    ],
                    required: ['number1', 'number2']
                )
            )
        ]
    ))
    ->startChat();

$response = $chat->sendMessage('What is 4 + 3?');

if ($response->parts()[0]->functionCall !== null) {
    $functionResponse = handleFunctionCall($response->parts()[0]->functionCall);

    $response = $chat->sendMessage($functionResponse);
}

echo $response->text(); // 4 + 3 = 7
```

----------------------------------------

TITLE: Retrieving Specific Gemini Model Information (PHP)
DESCRIPTION: Illustrates using the `models()` method followed by `retrieve()` with a specific model name to get detailed information about that model, such as its name, version, display name, and description.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_15

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;

$response = Gemini::models()->retrieve('models/gemini-2.5-pro-preview-05-06');

$response->model;
//Gemini\Data\Model Object
//(
//    [name] => models/gemini-2.5-pro-preview-05-06
//    [version] => 2.5-preview-05-06
//    [displayName] => Gemini 2.5 Pro Preview 05-06
//    [description] => Preview release (May 6th, 2025) of Gemini 2.5 Pro
//    ...
//)
```

----------------------------------------

TITLE: Generating Content with Text and Image (PHP)
DESCRIPTION: Shows how to use the Gemini model with both text and image inputs. It uses `Gemini::generativeModel` and includes an image as a `Blob` object, encoding the image data in base64.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_4

LANGUAGE: php
CODE:
```
use Gemini\Data\Blob;
use Gemini\Enums\MimeType;
use Gemini\Laravel\Facades\Gemini;

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->generateContent([
        'What is this picture?',
        new Blob(
            mimeType: MimeType::IMAGE_JPEG,
            data: base64_encode(
                file_get_contents('https://storage.googleapis.com/generativeai-downloads/images/scones.jpg')
            )
        )
    ]);

$result->text(); //  The picture shows a table with a white tablecloth. On the table are two cups of coffee, a bowl of blueberries, a silver spoon, and some flowers. There are also some blueberry scones on the table.
```

----------------------------------------

TITLE: Install Dependencies with Composer (Bash)
DESCRIPTION: Installs the project's development dependencies using the Composer package manager. This command should be run after cloning the repository.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer install
```

----------------------------------------

TITLE: Listing Available Gemini Models (PHP)
DESCRIPTION: Demonstrates calling the `models()` method followed by `list()` to retrieve a list of available models from the Gemini API. It shows how to access the resulting list of model objects.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_14

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;

$response = Gemini::models()->list();

$response->models;
//[
//    [0] => Gemini\Data\Model Object
//        (
//            [name] => models/gemini-2.0-flash
//            [version] => 2.0
//            [displayName] => Gemini 2.0 Flash
//            [description] => Gemini 2.0 Flash
//            ...
//        )
//    [1] => Gemini\Data\Model Object
//        (
//            [name] => models/gemini-2.5-pro-preview-05-06
//            [version] => 2.5-preview-05-06
//            [displayName] => Gemini 2.5 Pro Preview 05-06
//            [description] => Preview release (May 6th, 2025) of Gemini 2.5 Pro
//            ...
//        )
//    [2] => Gemini\Data\Model Object
//        (
//            [name] => models/text-embedding-004
//            [version] => 004
//            [displayName] => Text Embedding 004
//            [description] => Obtain a distributed representation of a text.
//            ...
//        )
//]
```

----------------------------------------

TITLE: Uploading Files to Gemini Storage (PHP)
DESCRIPTION: Illustrates the process of uploading a file, such as a video, to Gemini storage using `Gemini::files()->upload`. It includes polling the file's metadata to check its processing state until it's complete or failed.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_5

LANGUAGE: php
CODE:
```
use Gemini\Enums\FileState;
use Gemini\Enums\MimeType;
use Gemini\Laravel\Facades\Gemini;

$files = Gemini::files();
echo "Uploading\n";
$meta = $files->upload(
    filename: 'video.mp4',
    mimeType: MimeType::VIDEO_MP4,
    displayName: 'Video'
);
echo "Processing";
do {
    echo ".";
    sleep(2);
    $meta = $files->metadataGet($meta->uri);
} while (!$meta->state->complete());
echo "\n";

if ($meta->state == FileState::Failed) {
    die("Upload failed:\n" . json_encode($meta->toArray(), JSON_PRETTY_PRINT));
}

echo "Processing complete\n" . json_encode($meta->toArray(), JSON_PRETTY_PRINT);
echo "\n{$meta->uri}";
```

----------------------------------------

TITLE: Installing Google Gemini PHP for Laravel via Composer
DESCRIPTION: Installs the Google Gemini PHP client package for Laravel using Composer. This is the first step to integrate the Gemini API into a Laravel project.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require google-gemini-php/laravel
```

----------------------------------------

TITLE: Running Laravel Gemini Installation Command
DESCRIPTION: Executes the Laravel Artisan command to publish the Gemini configuration file (`config/gemini.php`) and append environment variables to the `.env` file.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
php artisan gemini:install
```

----------------------------------------

TITLE: Streaming Content Generation (PHP)
DESCRIPTION: Shows how to use streaming to receive partial responses from the Gemini model as they are generated, rather than waiting for the complete response. It uses `streamGenerateContent` and iterates over the resulting stream.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_8

LANGUAGE: php
CODE:
```
$stream = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->streamGenerateContent('Write long a story about a magic backpack.');

foreach ($stream as $response) {
    echo $response->text();
}
```

----------------------------------------

TITLE: Run Code Linting with Composer (Bash)
DESCRIPTION: Executes the code style checker (linter) for the project using Composer. This helps ensure code adheres to the defined coding standards.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: bash
CODE:
```
composer lint
```

----------------------------------------

TITLE: Conducting Multi-turn Conversations (PHP)
DESCRIPTION: Explains how to initiate and manage a multi-turn chat session with the Gemini model using `Gemini::chat()`. It shows how to start a chat with initial history and send subsequent messages, retrieving the model's responses.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_7

LANGUAGE: php
CODE:
```
use Gemini\Data\Content;
use Gemini\Enums\Role;
use Gemini\Laravel\Facades\Gemini;

$chat = Gemini::chat(model: 'gemini-2.0-flash')
    ->startChat(history: [
        Content::parse(part: 'The stories you write about what I have to say should be one line. Is that clear?'),
        Content::parse(part: 'Yes, I understand. The stories I write about your input should be one line long.', role: Role::MODEL)
    ]);

$response = $chat->sendMessage('Create a story set in a quiet village in 1600s France');
echo $response->text(); // Amidst rolling hills and winding cobblestone streets, the tranquil village of Beausoleil whispered tales of love, intrigue, and the magic of everyday life in 17th century France.

$response = $chat->sendMessage('Rewrite the same story in 1600s England');
echo $response->text(); // In the heart of England's lush countryside, amidst emerald fields and thatched-roof cottages, the village of Willowbrook unfolded a tapestry of love, mystery, and the enchantment of ordinary days in the 17th century.


```

----------------------------------------

TITLE: Count Tokens in Text with Gemini (PHP)
DESCRIPTION: This snippet demonstrates how to use the `countTokens` method provided by the Gemini facade to get the total number of tokens in a given string. This is useful for managing prompt length and understanding model costs.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_11

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;

$response = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->countTokens('Write a story about a magic backpack.');

echo $response->totalTokens; // 9
```

----------------------------------------

TITLE: Upgrading Google Gemini PHP for Laravel to v2.0
DESCRIPTION: Updates the Google Gemini PHP client package for Laravel to version 2.0 using Composer. This version introduces support for new features like structured output and function calling.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer require google-gemini-php/laravel:^2.0
```

----------------------------------------

TITLE: Faking Gemini Error Response in PHP
DESCRIPTION: Explains how to simulate an API error by providing a `Throwable` object, such as `Gemini\Exceptions\ErrorException`, to the `Gemini::fake()` method. This allows testing error handling logic in the application.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_19

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;
use Gemini\Exceptions\ErrorException;

Gemini::fake([
    new ErrorException([
        'message' => 'The model `gemini-basic` does not exist',
        'status' => 'INVALID_ARGUMENT',
        'code' => 400,
    ]),
]);

// the `ErrorException` will be thrown
Gemini::generativeModel(model: 'gemini-2.0-flash')->generateContent('test');
```

----------------------------------------

TITLE: Run All Tests with Composer (Bash)
DESCRIPTION: Runs all configured test suites for the project using Composer. This includes unit tests, type checks, and potentially other test types.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer test
```

----------------------------------------

TITLE: Run Unit Tests with Composer (Bash)
DESCRIPTION: Executes only the unit test suite for the project using Composer. This is useful for quickly running isolated tests.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/CONTRIBUTING.md#_snippet_4

LANGUAGE: bash
CODE:
```
composer test:unit
```

----------------------------------------

TITLE: Configure Gemini for JSON Output (PHP)
DESCRIPTION: This snippet shows how to configure the Gemini model to return responses in a structured JSON format. It uses `GenerationConfig` to set the `responseMimeType` to `APPLICATION_JSON` and defines a `responseSchema` to specify the expected structure of the JSON output, including data types and required properties.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_9

LANGUAGE: php
CODE:
```
use Gemini\Data\GenerationConfig;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;
use Gemini\Enums\ResponseMimeType;
use Gemini\Laravel\Facades\Gemini;

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')
    ->withGenerationConfig(
        generationConfig: new GenerationConfig(
            responseMimeType: ResponseMimeType::APPLICATION_JSON,
            responseSchema: new Schema(
                type: DataType::ARRAY,
                items: new Schema(
                    type: DataType::OBJECT,
                    properties: [
                        'recipe_name' => new Schema(type: DataType::STRING),
                        'cooking_time_in_minutes' => new Schema(type: DataType::INTEGER)
                    ],
                    required: ['recipe_name', 'cooking_time_in_minutes'],
                )
            )
        )
    )
    ->generateContent('List 5 popular cookie recipes with cooking time');

$result->json();
```

----------------------------------------

TITLE: Faking Standard Gemini Response in PHP
DESCRIPTION: Shows how to configure the Gemini fake client to return a predefined `GenerateContentResponse` for a standard `generateContent` call. It uses `Gemini::fake()` with an array containing a fake response object created via `GenerateContentResponse::fake()`.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_16

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;
use Gemini\Responses\GenerativeModel\GenerateContentResponse;

Gemini::fake([
    GenerateContentResponse::fake([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => 'success',
                        ],
                    ],
                ],
            ],
        ],
    ]),
]);

$result = Gemini::generativeModel(model: 'gemini-2.0-flash')->generateContent('test');

expect($result->text())->toBe('success');
```

----------------------------------------

TITLE: Getting Text Embedding with Gemini (PHP)
DESCRIPTION: Illustrates using the `embeddingModel` method with the `text-embedding-004` model and the `embedContent` method to obtain a text embedding. It shows how to access the resulting embedding values.
SOURCE: https://github.com/google-gemini-php/laravel/blob/main/README.md#_snippet_13

LANGUAGE: php
CODE:
```
use Gemini\Laravel\Facades\Gemini;

$response = Gemini::embeddingModel('text-embedding-004')
    ->embedContent("Write a story about a magic backpack.");

print_r($response->embedding->values);
//[
//    [0] => 0.008624583
//    [1] => -0.030451821
//    [2] => -0.042496547
//    [3] => -0.029230341
//    [4] => 0.05486475
//    [5] => 0.006694871
//    [6] => 0.004025645
//    [7] => -0.007294857
//    [8] => 0.0057651913
//    ...
//]
```