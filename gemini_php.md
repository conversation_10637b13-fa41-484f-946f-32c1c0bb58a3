TITLE: Installing Gemini PHP Client via Composer
DESCRIPTION: This command installs the Google Gemini PHP client library using <PERSON>, the PHP package manager. It's the first step to integrate the Gemini API into your PHP project.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer require google-gemini-php/client
```

----------------------------------------

TITLE: Initializing and Using Gemini Generative Model in PHP
DESCRIPTION: This snippet demonstrates how to initialize the Gemini client with an API key and use the `generativeModel` method to send text prompts. It shows both direct model string usage and a helper method for generating model names, retrieving the text response from the result object.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_3

LANGUAGE: php
CODE:
```
use Gemini\Enums\ModelVariation;
use Gemini\GeminiHelper;
use Gemini;

$yourApiKey = getenv('YOUR_API_KEY');
$client = Gemini::client($yourApiKey);

$result = $client->generativeModel(model: 'gemini-2.0-flash')->generateContent('Hello');
$result->text(); // Hello! How can I assist you today?

// Helper method usage
$result = $client->generativeModel(
    model: GeminiHelper::generateGeminiModel(
        variation: ModelVariation::FLASH,
        generation: 2.5,
        version: "preview-04-17"
    ), // models/gemini-2.5-flash-preview-04-17
);
$result->text(); // Hello! How can I assist you today?
```

----------------------------------------

TITLE: Generating Text-Only Content with Gemini PHP
DESCRIPTION: This snippet demonstrates how to generate a text-based response from the Gemini model by providing a simple text input. It initializes the Gemini client with an API key and then calls the `generateContent` method on a specified model.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_5

LANGUAGE: PHP
CODE:
```
use Gemini;

$yourApiKey = getenv('YOUR_API_KEY');
$client = Gemini::client($yourApiKey);

$result = $client->generativeModel(model: 'gemini-2.0-flash')->generateContent('Hello');

$result->text(); // Hello! How can I assist you today?
```

----------------------------------------

TITLE: Implementing Function Calling with Gemini PHP
DESCRIPTION: This example illustrates how to enable the Gemini model to call custom functions. It defines an `addition` function using `FunctionDeclaration` and provides a `handleFunctionCall` PHP function to execute the model's requested function call, returning the result back to the model. This allows the model to perform actions or calculations beyond its inherent capabilities.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_12

LANGUAGE: php
CODE:
```
<?php

use Gemini\Data\Content;
use Gemini\Data\FunctionCall;
use Gemini\Data\FunctionDeclaration;
use Gemini\Data\FunctionResponse;
use Gemini\Data\Part;
use Gemini\Data\Schema;
use Gemini\Data\Tool;
use Gemini\Enums\DataType;
use Gemini\Enums\Role;

function handleFunctionCall(FunctionCall $functionCall): Content
{
    if ($functionCall->name === 'addition') {
        return new Content(
            parts: [
                new Part(
                    functionResponse: new FunctionResponse(
                        name: 'addition',
                        response: ['answer' => $functionCall->args['number1'] + $functionCall->args['number2']],
                    )
                )
            ],
            role: Role::USER
        );
    }

    //Handle other function calls
}

$chat = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->withTool(new Tool(
        functionDeclarations: [
            new FunctionDeclaration(
                name: 'addition',
                description: 'Performs addition',
                parameters: new Schema(
                    type: DataType::OBJECT,
                    properties: [
                        'number1' => new Schema(
                            type: DataType::NUMBER,
                            description: 'First number'
                        ),
                        'number2' => new Schema(
                            type: DataType::NUMBER,
                            description: 'Second number'
                        ),
                    ],
                    required: ['number1', 'number2']
                )
            )
        ]
    ))
    ->startChat();

$response = $chat->sendMessage('What is 4 + 3?');

if ($response->parts()[0]->functionCall !== null) {
    $functionResponse = handleFunctionCall($response->parts()[0]->functionCall);

    $response = $chat->sendMessage($functionResponse);
}

echo $response->text(); // 4 + 3 = 7
```

----------------------------------------

TITLE: Configuring Structured JSON Output with Gemini PHP
DESCRIPTION: This snippet demonstrates how to configure the Gemini model to return structured JSON output instead of unstructured text. It specifies the `responseMimeType` as `APPLICATION_JSON` and defines a `responseSchema` for an array of objects, each containing `recipe_name` (string) and `cooking_time_in_minutes` (integer) fields. This is useful for applications requiring machine-readable data.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_11

LANGUAGE: php
CODE:
```
use Gemini\Data\GenerationConfig;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;
use Gemini\Enums\ResponseMimeType;

$result = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->withGenerationConfig(
        generationConfig: new GenerationConfig(
            responseMimeType: ResponseMimeType::APPLICATION_JSON,
            responseSchema: new Schema(
                type: DataType::ARRAY,
                items: new Schema(
                    type: DataType::OBJECT,
                    properties: [
                        'recipe_name' => new Schema(type: DataType::STRING),
                        'cooking_time_in_minutes' => new Schema(type: DataType::INTEGER)
                    ],
                    required: ['recipe_name', 'cooking_time_in_minutes'],
                )
            )
        )
    )
    ->generateContent('List 5 popular cookie recipes with cooking time');

$result->json();
```

----------------------------------------

TITLE: Building Multi-turn Conversations (Chat) with Gemini PHP
DESCRIPTION: This snippet demonstrates how to initiate and continue a freeform, multi-turn conversation with the Gemini model. It sets up an initial chat history using `Gemini\Data\Content` and then sends subsequent messages, maintaining context across turns.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_9

LANGUAGE: PHP
CODE:
```
use Gemini\Data\Content;
use Gemini\Enums\Role;

$chat = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->startChat(history: [
        Content::parse(part: 'The stories you write about what I have to say should be one line. Is that clear?'),
        Content::parse(part: 'Yes, I understand. The stories I write about your input should be one line long.', role: Role::MODEL)
    ]);

$response = $chat->sendMessage('Create a story set in a quiet village in 1600s France');
echo $response->text(); // Amidst rolling hills and winding cobblestone streets, the tranquil village of Beausoleil whispered tales of love, intrigue, and the magic of everyday life in 17th century France.

$response = $chat->sendMessage('Rewrite the same story in 1600s England');
echo $response->text(); // In the heart of England's lush countryside, amidst emerald fields and thatched-roof cottages, the village of Willowbrook unfolded a tapestry of love, mystery, and the enchantment of ordinary days in the 17th century.
```

----------------------------------------

TITLE: Configuring Gemini Model and Generating Content (PHP)
DESCRIPTION: This snippet demonstrates how to configure the Google Gemini model with custom safety settings and generation parameters. It initializes SafetySetting objects to control harmful content blocking and a GenerationConfig object for output control (e.g., maxOutputTokens, temperature). Finally, it uses these configurations with the generativeModel to generate a story.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_14

LANGUAGE: PHP
CODE:
```
use Gemini\Data\GenerationConfig;
use Gemini\Enums\HarmBlockThreshold;
use Gemini\Data\SafetySetting;
use Gemini\Enums\HarmCategory;

$safetySettingDangerousContent = new SafetySetting(
    category: HarmCategory::HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold::BLOCK_ONLY_HIGH
);

$safetySettingHateSpeech = new SafetySetting(
    category: HarmCategory::HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold::BLOCK_ONLY_HIGH
);

$generationConfig = new GenerationConfig(
    stopSequences: [
        'Title',
    ],
    maxOutputTokens: 800,
    temperature: 1,
    topP: 0.8,
    topK: 10
);

$generativeModel = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->withSafetySetting($safetySettingDangerousContent)
    ->withSafetySetting($safetySettingHateSpeech)
    ->withGenerationConfig($generationConfig)
    ->generateContent('Write a story about a magic backpack.');
```

----------------------------------------

TITLE: Streaming Content Generation from Gemini PHP
DESCRIPTION: This example shows how to use streaming to receive partial results from the Gemini model as they become available, rather than waiting for the entire response. This can lead to faster perceived interactions, iterating over the stream to output text chunks.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_10

LANGUAGE: PHP
CODE:
```
$stream = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->streamGenerateContent('Write long a story about a magic backpack.');

foreach ($stream as $response) {
    echo $response->text();
}
```

----------------------------------------

TITLE: Batch Embedding Multiple Text Contents with Gemini (PHP)
DESCRIPTION: This snippet illustrates how to generate embeddings for multiple text inputs simultaneously using the batchEmbedContents method with the text-embedding-004 model. It takes multiple strings as arguments and returns an array of ContentEmbedding objects, each containing the vectorized representation (values) for its corresponding input text.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_16

LANGUAGE: PHP
CODE:
```
$response = $client
    ->embeddingModel('text-embedding-004')
    ->batchEmbedContents("Bu bir testtir", "Deneme123");

print_r($response->embeddings);
// [
// [0] => Gemini\Data\ContentEmbedding Object
// (
//     [values] => Array
//         (
//         [0] => 0.035855837
//         [1] => -0.049537655
//         [2] => -0.06834927
//         [3] => -0.010445258
//         [4] => 0.044641383
//         [5] => 0.031156342
//         [6] => -0.007810312
//         [7] => -0.0106866965
//         ...
//         ),
// ),
// [1] => Gemini\Data\ContentEmbedding Object
// (
//     [values] => Array
//         (
//         [0] => 0.035855837
//         [1] => -0.049537655
//         [2] => -0.06834927
//         [3] => -0.010445258
//         [4] => 0.044641383
//         [5] => 0.031156342
//         [6] => -0.007810312
//         [7] => -0.0106866965
//         ...
//         ),
// ),
// ]
```

----------------------------------------

TITLE: Embedding Single Text Content with Gemini (PHP)
DESCRIPTION: This snippet demonstrates how to generate an embedding for a single piece of text using the text-embedding-004 model. The embedContent method processes the input string and returns an embedding object containing a list of floating-point values representing the text in a vectorized form, useful for similarity comparisons.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_15

LANGUAGE: PHP
CODE:
```
$response = $client
    ->embeddingModel('text-embedding-004')
    ->embedContent("Write a story about a magic backpack.");

print_r($response->embedding->values);
//[
//    [0] => 0.008624583
//    [1] => -0.030451821
//    [2] => -0.042496547
//    [3] => -0.029230341
//    [4] => 0.05486475
//    [5] => 0.006694871
//    [6] => 0.004025645
//    [7] => -0.007294857
//    [8] => 0.0057651913
//    ...
//]
```

----------------------------------------

TITLE: Generating Content with Text and Image Input in Gemini PHP
DESCRIPTION: This example illustrates how to send both text prompts and image data to the Gemini model to generate a response. It uses `Gemini\Data\Blob` to encapsulate the image, which is base64-encoded from a URL, and specifies its MIME type.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_6

LANGUAGE: PHP
CODE:
```
use Gemini\Data\Blob;
use Gemini\Enums\MimeType;

$result = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->generateContent([
        'What is this picture?',
        new Blob(
            mimeType: MimeType::IMAGE_JPEG,
            data: base64_encode(
                file_get_contents('https://storage.googleapis.com/generativeai-downloads/images/scones.jpg')
            )
        )
    ]);

$result->text(); //  The picture shows a table with a white tablecloth. On the table are two cups of coffee, a bowl of blueberries, a silver spoon, and some flowers. There are also some blueberry scones on the table.
```

----------------------------------------

TITLE: Uploading Files to Gemini Storage with PHP
DESCRIPTION: This snippet demonstrates how to upload a file (e.g., a video) to Gemini storage for later reference in prompts. It shows the process of uploading, polling for completion, and handling potential upload failures, providing metadata upon success.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_7

LANGUAGE: PHP
CODE:
```
use Gemini\Enums\FileState;
use Gemini\Enums\MimeType;

$files = $client->files();
echo "Uploading\n";
$meta = $files->upload(
    filename: 'video.mp4',
    mimeType: MimeType::VIDEO_MP4,
    displayName: 'Video'
);
echo "Processing";
do {
    echo ".";
    sleep(2);
    $meta = $files->metadataGet($meta->uri);
} while (!$meta->state->complete());
echo "\n";

if ($meta->state == FileState::Failed) {
    die("Upload failed:\n" . json_encode($meta->toArray(), JSON_PRETTY_PRINT));
}

echo "Processing complete\n" . json_encode($meta->toArray(), JSON_PRETTY_PRINT);
echo "\n{$meta->uri}";
```

----------------------------------------

TITLE: Processing Video Content with Text Prompts in Gemini PHP
DESCRIPTION: This code shows how to generate AI-powered descriptions from video content by referencing an already uploaded video file. It uses `Gemini\Data\UploadedFile` to link to the video by its URI and specifies its MIME type along with a text prompt.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_8

LANGUAGE: PHP
CODE:
```
use Gemini\Data\UploadedFile;
use Gemini\Enums\MimeType;

$result = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->generateContent([
        'What is this video?',
        new UploadedFile(
            fileUri: '123-456', // accepts just the name or the full URI
            mimeType: MimeType::VIDEO_MP4
        )
    ]);

$result->text(); //  The picture shows a table with a white tablecloth. On the table are two cups of coffee, a bowl of blueberries, a silver spoon, and some flowers. There are also some blueberry scones on the table.
```

----------------------------------------

TITLE: Counting Tokens with Gemini PHP
DESCRIPTION: This snippet shows how to use the Gemini PHP client to count the number of tokens in a given text prompt. This is useful for managing API costs and ensuring prompts fit within model limits, especially for long inputs.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_13

LANGUAGE: php
CODE:
```
$response = $client
    ->generativeModel(model: 'gemini-2.0-flash')
    ->countTokens('Write a story about a magic backpack.');

echo $response->totalTokens; // 9
```

----------------------------------------

TITLE: Configuring a Custom Gemini PHP Client
DESCRIPTION: This example illustrates how to create a highly customized Gemini client instance using the `factory` method. It allows setting a custom base URL, adding HTTP headers and query parameters, providing a specific HTTP client (e.g., Guzzle), and defining a custom stream handler for advanced control over network requests.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_4

LANGUAGE: php
CODE:
```
use Gemini;

$yourApiKey = getenv('YOUR_API_KEY');

$client = Gemini::factory()
    ->withApiKey($yourApiKey)
    ->withBaseUrl('https://generativelanguage.example.com/v1beta') // default: https://generativelanguage.googleapis.com/v1beta/
    ->withHttpHeader('X-My-Header', 'foo')
    ->withQueryParam('my-param', 'bar')
    ->withHttpClient(new \GuzzleHttp\Client([]))  // default: HTTP client found using PSR-18 HTTP Client Discovery
    ->withStreamHandler(fn(RequestInterface $request): ResponseInterface => $client->send($request, [
        'stream' => true // Allows to provide a custom stream handler for the http client.
    ]))
    ->make();
```

----------------------------------------

TITLE: Upgrading Gemini PHP Client to Version 2.0
DESCRIPTION: This command updates the Google Gemini PHP client to version 2.0, which introduces support for the Gemini v1beta API and new features like structured output, file uploads, and function calling.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer require google-gemini-php/client:^2.0
```

----------------------------------------

TITLE: Configuring HTTP Client Timeout for Gemini PHP Client
DESCRIPTION: This snippet demonstrates how to configure a custom timeout for the HTTP client used by the Gemini PHP client, specifically using Guzzle. It shows how to pass a `GuzzleHttp\Client` instance with a specified `timeout` value to the `withHttpClient` method during client factory instantiation. This is useful for preventing request timeouts, especially with long-running API calls.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_19

LANGUAGE: php
CODE:
```
Gemini::factory()
    ->withApiKey($apiKey)
    ->withHttpClient(new \GuzzleHttp\Client(['timeout' => $timeout]))
    ->make();
```

----------------------------------------

TITLE: Installing PSR-18 HTTP Client (Guzzle)
DESCRIPTION: This command installs Guzzle, a popular HTTP client, to satisfy the PSR-18 HTTP client requirement. It's necessary if `php-http/discovery` is not allowed to run or if a PSR-18 client is not already present in the project.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
composer require guzzlehttp/guzzle
```

----------------------------------------

TITLE: Faking API Error Response in PHP
DESCRIPTION: This snippet demonstrates how to simulate an API error by providing an `ErrorException` object to `ClientFake`. This allows testing error handling logic in the application without relying on actual API failures, specifying error message, status, and code.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_23

LANGUAGE: php
CODE:
```
use Gemini\Testing\ClientFake;
use Gemini\Exceptions\ErrorException;

$client = new ClientFake([
    new ErrorException([
        'message' => 'The model `gemini-basic` does not exist',
        'status' => 'INVALID_ARGUMENT',
        'code' => 400,
    ]),
]);

// the `ErrorException` will be thrown
$client->generativeModel(model: 'gemini-2.0-flash')->generateContent('test');
```

----------------------------------------

TITLE: Faking Streamed Content Generation Response in PHP
DESCRIPTION: This snippet illustrates how to fake a streamed `generateContent` response using `ClientFake`. It utilizes `GenerateContentResponse::fakeStream()` to simulate a streaming API call, enabling tests for asynchronous or partial content delivery scenarios.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_21

LANGUAGE: php
CODE:
```
use Gemini\Testing\ClientFake;
use Gemini\Responses\GenerativeModel\GenerateContentResponse;

$client = new ClientFake([
    GenerateContentResponse::fakeStream(),
]);

$result = $client->generativeModel(model: 'gemini-2.0-flash')->streamGenerateContent('Hello');

expect($response->getIterator()->current())
    ->text()->toBe('In the bustling city of Aethelwood, where the cobblestone streets whispered');
```

----------------------------------------

TITLE: Asserting Sent Requests in PHP
DESCRIPTION: This snippet provides various examples of asserting that specific API requests were sent using the `ClientFake` instance. It covers asserting `list` methods for `Models` and `GenerativeModel` resources, asserting `generateContent` calls with specific parameters, asserting the count of sent requests, and asserting that no requests or no specific requests were sent.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_22

LANGUAGE: php
CODE:
```
// assert list models request was sent
use Gemini\Resources\GenerativeModel;
use Gemini\Resources\Models;

$fake->models()->assertSent(callback: function ($method) {
    return $method === 'list';
});
// or
$fake->assertSent(resource: Models::class, callback: function ($method) {
    return $method === 'list';
});

$fake->geminiPro()->assertSent(function (string $method, array $parameters) {
    return $method === 'generateContent' &&
        $parameters[0] === 'Hello';
});
// or
$fake->assertSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash', callback: function (string $method, array $parameters) {
    return $method === 'generateContent' &&
        $parameters[0] === 'Hello';
});

// assert 2 generative model requests were sent
$client->assertSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash', callback: 2);
// or
$client->generativeModel(model: 'gemini-2.0-flash')->assertSent(2);

// assert no generative model requests were sent
$client->assertNotSent(resource: GenerativeModel::class, model: 'gemini-2.0-flash');
// or
$client->generativeModel(model: 'gemini-2.0-flash')->assertNotSent();

// assert no requests were sent
$client->assertNothingSent();
```

----------------------------------------

TITLE: Faking Successful Content Generation Response in PHP
DESCRIPTION: This snippet demonstrates how to initialize `ClientFake` to mock a successful `generateContent` API call. It shows how to construct a `GenerateContentResponse` with specific text content using the `fake()` method, allowing for predictable test outcomes without actual API calls.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_20

LANGUAGE: php
CODE:
```
use Gemini\Testing\ClientFake;
use Gemini\Responses\GenerativeModel\GenerateContentResponse;

$client = new ClientFake([
    GenerateContentResponse::fake([
        'candidates' => [
            [
                'content' => [
                    'parts' => [
                        [
                            'text' => 'success',
                        ],
                    ],
                ],
            ],
        ],
    ]),
]);

$result = $fake->generativeModel(model: 'gemini-2.0-flash')->generateContent('test');

expect($result->text())->toBe('success');
```

----------------------------------------

TITLE: Listing Gemini Models in PHP
DESCRIPTION: This snippet demonstrates how to programmatically list available Gemini models using the PHP client. It shows how to paginate results using `pageSize` and `nextPageToken` parameters to control the number of models returned per page and retrieve subsequent pages. The response contains an array of `Gemini\Data\Model` objects, each providing details like name, version, and display name.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_17

LANGUAGE: php
CODE:
```
$response = $client->models()->list(pageSize: 3, nextPageToken: 'ChFtb2RlbHMvZ2VtaW5pLXBybw==');

$response->models;
//[
//    [0] => Gemini\Data\Model Object
//        (
//            [name] => models/gemini-2.0-flash
//            [version] => 2.0
//            [displayName] => Gemini 2.0 Flash
//            [description] => Gemini 2.0 Flash
//            ...
//        )
//    [1] => Gemini\Data\Model Object
//        (
//            [name] => models/gemini-2.5-pro-preview-05-06
//            [version] => 2.5-preview-05-06
//            [displayName] => Gemini 2.5 Pro Preview 05-06
//            [description] => Preview release (May 6th, 2025) of Gemini 2.5 Pro
//            ...
//        )
//    [2] => Gemini\Data\Model Object
//        (
//            [name] => models/text-embedding-004
//            [version] => 004
//            [displayName] => Text Embedding 004
//            [description] => Obtain a distributed representation of a text.
//            ...
//        )
//]
```

LANGUAGE: php
CODE:
```
$response->nextPageToken // Chltb2RlbHMvZ2VtaW5pLTEuMC1wcm8tMDAx
```

----------------------------------------

TITLE: Retrieving Specific Gemini Model Information in PHP
DESCRIPTION: This snippet illustrates how to retrieve detailed information for a specific Gemini model using its name. The `retrieve` method fetches data such as the model's version, display name, and description. The response provides a `Gemini\Data\Model` object containing comprehensive details about the requested model.
SOURCE: https://github.com/google-gemini-php/client/blob/main/README.md#_snippet_18

LANGUAGE: php
CODE:
```

$response = $client->models()->retrieve('models/gemini-2.5-pro-preview-05-06');

$response->model;
//Gemini\Data\Model Object
//(
//    [name] => models/gemini-2.5-pro-preview-05-06
//    [version] => 2.5-preview-05-06
//    [displayName] => Gemini 2.5 Pro Preview 05-06
//    [description] => Preview release (May 6th, 2025) of Gemini 2.5 Pro
//    ...
//)
```

----------------------------------------

TITLE: Installing Dependencies - Bash
DESCRIPTION: Installs the project's development dependencies using Composer. This command should be run after cloning the repository to set up the development environment.
SOURCE: https://github.com/google-gemini-php/client/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: bash
CODE:
```
composer install
```

----------------------------------------

TITLE: Running Code Linting - Bash
DESCRIPTION: Executes the code style linter using Composer to ensure code adheres to project standards. This should be run before submitting a pull request.
SOURCE: https://github.com/google-gemini-php/client/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: bash
CODE:
```
composer lint
```

----------------------------------------

TITLE: Running Unit Tests - Bash
DESCRIPTION: Executes only the unit test suite using Composer. This is useful for quickly testing individual components.
SOURCE: https://github.com/google-gemini-php/client/blob/main/CONTRIBUTING.md#_snippet_4

LANGUAGE: bash
CODE:
```
composer test:unit
```

----------------------------------------

TITLE: Checking Types - Bash
DESCRIPTION: Runs type checking tests using Composer. This command verifies the type safety of the code.
SOURCE: https://github.com/google-gemini-php/client/blob/main/CONTRIBUTING.md#_snippet_3

LANGUAGE: bash
CODE:
```
composer test:types
```

----------------------------------------

TITLE: Running All Tests - Bash
DESCRIPTION: Executes all test suites defined in the project using Composer. This is a comprehensive check of the codebase's functionality.
SOURCE: https://github.com/google-gemini-php/client/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: bash
CODE:
```
composer test
```