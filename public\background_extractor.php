<?php
/**
 * Background Text Extractor using Gemini AI
 * Processes file text extraction in the background using Gemini's multimodal capabilities
 */

// Include CodeIgniter bootstrap
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
require_once __DIR__ . '/../app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Start CodeIgniter application
$app = Config\Services::codeigniter();
$app->initialize();

// Get command line arguments
if ($argc < 3) {
    echo "Usage: php background_extractor.php <process_id> <file_path>\n";
    exit(1);
}

$processId = $argv[1];
$filePath = $argv[2];

echo "=== DERS Background Text Extractor (Gemini AI) ===\n";
echo "Process ID: {$processId}\n";
echo "File Path: {$filePath}\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n";
echo "================================================\n\n";

// Initialize session for progress tracking
$session = \Config\Services::session();

// Update initial status
$session->set("file_process_{$processId}", [
    'status' => 'processing',
    'message' => 'Initializing Gemini AI text extraction...',
    'progress' => 20
]);

try {
    echo "STEP 1: File Analysis\n";
    echo "Checking file: {$filePath}\n";

    // Check if file exists
    if (!file_exists($filePath)) {
        throw new Exception("File not found: {$filePath}");
    }

    $fileSize = filesize($filePath);
    $mimeType = mime_content_type($filePath) ?: 'text/plain';

    echo "✓ File exists and is accessible\n";
    echo "✓ File size: " . number_format($fileSize / 1024, 2) . " KB\n";
    echo "✓ MIME type: {$mimeType}\n\n";

    // Update progress
    $session->set("file_process_{$processId}", [
        'status' => 'processing',
        'message' => 'File analyzed. Starting Gemini AI processing...',
        'progress' => 30
    ]);

    echo "STEP 2: Gemini AI Text Extraction\n";
    echo "Initializing Gemini AI service...\n";

    // Initialize Gemini service
    $geminiService = new \App\Services\GeminiService();

    echo "✓ Gemini AI service initialized\n";
    echo "✓ Using model: gemini-2.0-flash\n";
    echo "✓ Starting multimodal text extraction...\n";
    echo "✓ Processing method: " . ($fileSize > 20*1024*1024 ? "File API (large file)" : "Inline processing") . "\n\n";

    // Update progress
    $session->set("file_process_{$processId}", [
        'status' => 'processing',
        'message' => 'Gemini AI is processing the file...',
        'progress' => 50
    ]);

    // Extract text using Gemini AI
    echo "Calling Gemini AI for text extraction...\n";
    $extractedText = $geminiService->extractTextFromFile($filePath);

    if (empty($extractedText)) {
        throw new Exception("Gemini AI returned empty text extraction");
    }

    echo "✓ Gemini AI extraction completed successfully!\n";
    echo "✓ Text length: " . number_format(strlen($extractedText)) . " characters\n";
    echo "✓ Word count: " . number_format(str_word_count($extractedText)) . " words\n";
    echo "✓ Processing method: Gemini AI multimodal analysis\n";
    echo "✓ Format: Markdown formatted text\n\n";

    // Update progress
    $session->set("file_process_{$processId}", [
        'status' => 'processing',
        'message' => 'Text extracted. Updating database...',
        'progress' => 80
    ]);

    echo "STEP 3: Database Update\n";

    // Load database and update the file record
    $db = \Config\Database::connect();
    $filesModel = new \App\Models\FilesModel();

    // Find the file record by matching the file path
    $relativePath = str_replace(FCPATH, '', $filePath);
    $relativePath = str_replace('\\', '/', $relativePath); // Normalize path separators

    echo "Looking for file record with path: {$relativePath}\n";

    $fileRecord = $db->table('files')
        ->like('file_path', basename($filePath))
        ->orderBy('id', 'DESC')
        ->get()
        ->getRowArray();

    if ($fileRecord) {
        // Update the file record with extracted text
        $updateData = [
            'file_extracted_texts' => $extractedText,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $filesModel->update($fileRecord['id'], $updateData);
        echo "✓ Database updated successfully (File ID: {$fileRecord['id']})\n";
    } else {
        echo "⚠ Warning: Could not find file record in database\n";
        echo "⚠ File will be processed but database won't be updated\n";
    }

    // Update final status
    $session->set("file_process_{$processId}", [
        'status' => 'completed',
        'message' => 'Text extraction completed successfully!',
        'progress' => 100,
        'extracted_text' => substr($extractedText, 0, 500) . '...' // Store preview
    ]);

    echo "✓ Processing status: COMPLETED\n";
    echo "✓ Text extraction successful\n\n";

    echo "=== EXTRACTION SUMMARY ===\n";
    echo "Process ID: {$processId}\n";
    echo "File: " . basename($filePath) . "\n";
    echo "Method: Gemini AI (gemini-2.0-flash)\n";
    echo "Characters extracted: " . number_format(strlen($extractedText)) . "\n";
    echo "Words extracted: " . number_format(str_word_count($extractedText)) . "\n";
    echo "Status: SUCCESS\n";
    echo "Completed at: " . date('Y-m-d H:i:s') . "\n";
    echo "========================\n";

} catch (Exception $e) {
    echo "\n=== EXTRACTION FAILED ===\n";
    echo "Process ID: {$processId}\n";
    echo "File: " . basename($filePath) . "\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Failed at: " . date('Y-m-d H:i:s') . "\n";
    echo "========================\n";

    // Update session with error status
    $session->set("file_process_{$processId}", [
        'status' => 'failed',
        'message' => 'Text extraction failed: ' . $e->getMessage(),
        'progress' => 0,
        'error' => $e->getMessage()
    ]);

    // Log the error
    error_log("Background text extraction failed for file {$filePath}: " . $e->getMessage());
}

echo "\nBackground extractor script finished\n";
?>
