<?php
/**
 * Test script to verify the extraction progress route is working
 */

echo "=== Testing Extraction Progress Route ===\n\n";

// Test the route directly
$testProcessId = 'test_' . uniqid();
$testUrl = 'http://localhost/applicant/check-extraction-progress/' . $testProcessId;

echo "Testing URL: {$testUrl}\n";

// Use cURL to test the endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Status Code: {$httpCode}\n";

if ($error) {
    echo "cURL Error: {$error}\n";
} else {
    echo "Response: {$response}\n";
    
    // Try to decode JSON
    $jsonData = json_decode($response, true);
    if ($jsonData) {
        echo "JSON Response:\n";
        print_r($jsonData);
    } else {
        echo "Response is not valid JSON\n";
    }
}

echo "\n=== Test Complete ===\n";
?>
